import QtQuick
import QtQuick.Layouts
import Quickshell.Services.Pipewire
import "../modules"

Rectangle {
    id: volumeWidget
    objectName: "volumeWidget"
    width: Math.min(expanded ? expandedWidth : collapsedWidth, maxWidth)
    height: 18
    radius: 9
    color: "transparent"
    clip: true

    visible: Pipewire.defaultAudioSink !== null



    property bool expanded: false

    onExpandedChanged: {
        if (expanded) {
            collapseTimer.start()
        } else {
            collapseTimer.stop()
        }
    }
    property int collapsedWidth: volumeContent.width + 2
    property int expandedWidth: volumeContent.width + sliderWidth + 4
    property int maxWidth: 90
    property int blockWidth: 4
    property int blockSpacing: 1
    property int totalBlocks: 15
    property int sliderWidth: (blockWidth * totalBlocks) + (blockSpacing * (totalBlocks - 1))

    readonly property var audioSink: Pipewire.defaultAudioSink
    readonly property var audioNode: audioSink ? audioSink.audio : null
    readonly property real currentVolume: audioNode ? audioNode.volume : 0.0


    readonly property int volumePercent: Math.round(currentVolume * 100)
    readonly property bool isMuted: audioNode ? audioNode.muted : false

    PwObjectTracker {
        objects: [audioSink]
    }

    property var parentTheme: null
    property var theme: parentTheme

    RowLayout {
        id: volumeContent
        anchors.centerIn: parent
        spacing: 2

        Text {
            id: volumeIcon
            text: {
                if (isMuted) return "󰖁"
                if (volumePercent === 0) return "󰕿"
                if (volumePercent < 30) return "󰖀"
                if (volumePercent < 70) return "󰕾"
                return "󰕾"
            }
            color: isMuted ? (theme ? theme.textSecondary : "#999999") : (theme ? theme.textPrimary : "#f2f4f8")
            font.pixelSize: 14
            font.family: "JetBrains Mono Nerd Font, monospace"

            MouseArea {
                anchors.fill: parent
                acceptedButtons: Qt.LeftButton | Qt.RightButton
                onClicked: function(mouse) {
                    if (mouse.button === Qt.LeftButton) {
                        if (audioNode) {
                            audioNode.muted = !audioNode.muted
                        }
                    } else if (mouse.button === Qt.RightButton) {
                        launchPavucontrol()
                    }
                }
            }
        }

        Text {
            id: volumeText
            text: volumePercent + "%"
            color: isMuted ? (theme ? theme.textSecondary : "#999999") : (theme ? theme.textPrimary : "#f2f4f8")
            font.pixelSize: 10
            font.family: "JetBrains Mono, monospace"
            font.weight: Font.Medium
            visible: !expanded
        }

        Row {
            id: sliderContainer
            width: expanded ? sliderWidth : 0
            height: 14
            visible: expanded
            opacity: expanded ? 1 : 0
            Layout.alignment: Qt.AlignVCenter
            spacing: blockSpacing
            clip: true

            property int filledBlocks: Math.round((volumePercent / 100) * totalBlocks)

            Repeater {
                model: totalBlocks

                Rectangle {
                    width: blockWidth
                    height: 12
                    radius: 1
                    color: index < sliderContainer.filledBlocks ? (theme ? theme.accent : "#3ddbd9") : (theme ? theme.textSecondary : "#999999")
                    opacity: index < sliderContainer.filledBlocks ? 1.0 : 0.3

                    Behavior on color {
                        ColorAnimation { duration: 200 }
                    }

                    Behavior on opacity {
                        NumberAnimation { duration: 200 }
                    }
                }
            }

            MouseArea {
                anchors.fill: parent
                hoverEnabled: true

                property bool wasDragged: false
                property real startX: 0

                onPressed: function(mouse) {
                    wasDragged = false
                    startX = mouse.x
                }

                onPositionChanged: function(mouse) {
                    if (pressed) {
                        var dragDistance = Math.abs(mouse.x - startX)
                        if (dragDistance > 3) {
                            wasDragged = true
                        }
                        updateVolume(mouse.x)
                    }
                }

                onReleased: function(mouse) {
                    if (!wasDragged) {
                        var clickPosition = mouse.x / width
                        var currentVol = currentVolume
                        var targetVolume

                        if (clickPosition > currentVol) {
                            targetVolume = Math.min(1.0, currentVol + 0.05)
                        } else {
                            targetVolume = Math.max(0.0, currentVol - 0.05)
                        }

                        if (audioNode) {
                            audioNode.volume = targetVolume
                        }
                    }
                }

                function updateVolume(x) {
                    if (audioNode) {
                        var newVolume = Math.max(0, Math.min(1, x / width))
                        audioNode.volume = newVolume
                    }
                }
            }

            Behavior on width {
                NumberAnimation { duration: 300; easing.type: Easing.OutCubic }
            }

            Behavior on opacity {
                NumberAnimation { duration: 200 }
            }
        }
    }



    function launchPavucontrol() {
        var process = Qt.createQmlObject('
            import Quickshell.Io
            Process {
                command: ["pavucontrol"]
                running: true
            }
        ', volumeWidget, "pavucontrolProcess")
    }

    MouseArea {
        id: mouseArea
        anchors.fill: parent
        acceptedButtons: Qt.LeftButton | Qt.RightButton
        hoverEnabled: true
        drag.target: null

        property bool isDragging: false
        property real startX: 0

        onClicked: function(mouse) {
            if (mouse.button === Qt.RightButton) {
                launchPavucontrol()
            } else if (mouse.button === Qt.LeftButton && !isDragging) {

                expanded = !expanded
                if (expanded) {
                    collapseTimer.restart()
                }
            }
        }

        onPressed: function(mouse) {
            if (mouse.button === Qt.LeftButton) {
                isDragging = false
                startX = mouse.x
                expanded = true
                collapseTimer.stop()
            }
        }

        onPositionChanged: function(mouse) {
            if (pressed && mouse.buttons & Qt.LeftButton) {
                var dragDistance = Math.abs(mouse.x - startX)
                if (dragDistance > 5) {
                    isDragging = true

                    if (expanded && audioNode) {
                        var relativeX = Math.max(0, Math.min(mouse.x, width))
                        var volumeRatio = relativeX / width
                        var newVolume = Math.max(0.0, Math.min(1.0, volumeRatio))
                        audioNode.volume = newVolume
                    }
                }
            }
        }

        onReleased: function(mouse) {
            if (isDragging) {
                collapseTimer.restart()
            }
            isDragging = false
        }

        onWheel: function(wheel) {
            expanded = true
            collapseTimer.restart()

            if (audioNode) {
                var delta = wheel.angleDelta.y / 120
                var volumeChange = delta * 0.05
                var newVolume = Math.max(0.0, Math.min(1.0, currentVolume + volumeChange))
                audioNode.volume = newVolume
            }
        }

        onEntered: {
            expanded = true
            if (expanded) {
                collapseTimer.stop()
            }
        }

        onExited: {
            if (expanded) {
                collapseTimer.restart()
            }
        }
    }

    Timer {
        id: collapseTimer
        interval: 3000
        onTriggered: {
            if (!mouseArea.containsMouse) {
                expanded = false
            }
        }
    }



    Behavior on color {
        ColorAnimation { duration: 200 }
    }

    Behavior on width {
        NumberAnimation { duration: 300; easing.type: Easing.OutCubic }
    }
}
