pragma Singleton
import QtQuick
import Quickshell

Singleton {
    id: root

    property string currentActivePanel: ""
    property var registeredPanels: {}

    signal panelShown
    signal panelHidden
    signal allPanelsHidden

    function logIpc(message) {
        // Simple console logging for now
        console.log("[IPC] " + message);
    }

    function registerPanel(panelId, panelObject) {
        logIpc("registerPanel called with panelId: " + panelId + ", panelObject: " + panelObject);

        if (!panelId || !panelObject) {
            logIpc("Failed to register panel: invalid parameters - panelId: " + panelId + ", panelObject: " + panelObject);
            return false;
        }

        if (!registeredPanels) {
            logIpc("registeredPanels was undefined, reinitializing");
            registeredPanels = {};
        }

        if (registeredPanels[panelId]) {
            logIpc("Panel " + panelId + " already registered, updating reference");
        }

        registeredPanels[panelId] = {
            object: panelObject,
            visible: false,
            lastShown: 0
        };

        logIpc("Panel " + panelId + " registered successfully. Total panels: " + Object.keys(registeredPanels).length);
        return true;
    }

    function unregisterPanel(panelId) {
        if (!panelId || !registeredPanels[panelId]) {
            logIpc("Failed to unregister panel: " + panelId + " not found");
            return false;
        }

        if (currentActivePanel === panelId) {
            hidePanel(panelId);
        }

        delete registeredPanels[panelId];
        logIpc("Panel " + panelId + " unregistered successfully");
        return true;
    }

    function showPanel(panelId) {
        if (!panelId || !registeredPanels || !registeredPanels[panelId]) {
            logIpc("Failed to show panel: " + panelId + " not registered");
            return false;
        }

        if (currentActivePanel && currentActivePanel !== panelId) {
            hidePanel(currentActivePanel);
        }

        currentActivePanel = panelId;
        if (registeredPanels[panelId]) {
            registeredPanels[panelId].visible = true;
            registeredPanels[panelId].lastShown = Date.now();

            // Call the panel's showOverlay function if it exists
            var panelObject = registeredPanels[panelId].object;
            if (panelObject && panelObject.item && typeof panelObject.item.showOverlay === "function") {
                panelObject.item.showOverlay();
                logIpc("Called showOverlay() for panel: " + panelId);
            }
        }

        logIpc("Showing panel: " + panelId);
        panelShown();
        return true;
    }

    function hidePanel(panelId) {
        if (!panelId || !registeredPanels || !registeredPanels[panelId]) {
            logIpc("Failed to hide panel: " + panelId + " not registered");
            return false;
        }

        if (!registeredPanels[panelId].visible) {
            logIpc("Panel " + panelId + " already hidden");
            return true;
        }

        registeredPanels[panelId].visible = false;

        // Call the panel's hideOverlay function if it exists
        var panelObject = registeredPanels[panelId].object;
        if (panelObject && panelObject.item && typeof panelObject.item.hideOverlay === "function") {
            panelObject.item.hideOverlay();
            logIpc("Called hideOverlay() for panel: " + panelId);
        }

        if (currentActivePanel === panelId) {
            currentActivePanel = "";
        }

        logIpc("Hiding panel: " + panelId);
        panelHidden();
        return true;
    }

    function hideAllPanels() {
        var hiddenCount = 0;
        var panelIds = Object.keys(registeredPanels);

        logIpc("Hiding all panels, found " + panelIds.length + " registered panels");

        for (var i = 0; i < panelIds.length; i++) {
            var panelId = panelIds[i];
            if (registeredPanels[panelId].visible) {
                hidePanel(panelId);
                hiddenCount++;
            }
        }

        currentActivePanel = "";
        if (hiddenCount > 0) {
            logIpc("Hidden " + hiddenCount + " panels");
            allPanelsHidden();
        }

        return hiddenCount;
    }

    function getCurrentActivePanel() {
        return currentActivePanel;
    }

    function togglePanel(panelId) {
        if (!panelId || !registeredPanels || !registeredPanels[panelId]) {
            logIpc("Failed to toggle panel: " + panelId + " not registered");
            return false;
        }

        if (registeredPanels[panelId].visible) {
            return hidePanel(panelId);
        } else {
            return showPanel(panelId);
        }
    }

    function isAnyPanelVisible() {
        var panelIds = Object.keys(registeredPanels);
        for (var i = 0; i < panelIds.length; i++) {
            if (registeredPanels[panelIds[i]].visible) {
                return true;
            }
        }
        return false;
    }

    function validatePanels() {
        var panelIds = Object.keys(registeredPanels);
        var invalidPanels = [];

        for (var i = 0; i < panelIds.length; i++) {
            var panelId = panelIds[i];
            var panelData = registeredPanels[panelId];

            // Enhanced validation for IPC state tracking
            if (!validatePanelForIpc(panelId, panelData)) {
                invalidPanels.push(panelId);
                continue;
            }

            if (!panelData.object || typeof panelData.object !== "object") {
                invalidPanels.push(panelId);
                continue;
            }

            try {
                var testAccess = panelData.object.visible;
            } catch (e) {
                invalidPanels.push(panelId);
            }
        }

        for (var j = 0; j < invalidPanels.length; j++) {
            logIpc("Cleaning up invalid panel: " + invalidPanels[j]);
            unregisterPanel(invalidPanels[j]);
        }

        return invalidPanels.length;
    }

    function validatePanelForIpc(panelId, panelData) {
        try {
            // Check if panel data structure is valid for IPC
            if (!panelData || typeof panelData !== "object") {
                logIpc("Invalid panel data structure for " + panelId);
                return false;
            }

            // Check required properties for IPC state tracking
            if (typeof panelData.visible !== "boolean") {
                logIpc("Invalid visible property for panel " + panelId);
                return false;
            }

            if (typeof panelData.lastShown !== "number") {
                logIpc("Invalid lastShown property for panel " + panelId);
                return false;
            }

            return true;
        } catch (error) {
            logIpc("Error validating panel for IPC " + panelId + ": " + error.message);
            return false;
        }
    }

    function verifySynchronization() {
        var panelIds = Object.keys(registeredPanels);
        var discrepancies = 0;

        for (var i = 0; i < panelIds.length; i++) {
            var panelId = panelIds[i];
            var panelData = registeredPanels[panelId];

            if (panelData.object && typeof panelData.object === "object") {
                try {
                    var actualVisible = panelData.object.visible || false;
                    var trackedVisible = panelData.visible;

                    if (actualVisible !== trackedVisible) {
                        logIpc("State mismatch for panel " + panelId + ": tracked=" + trackedVisible + ", actual=" + actualVisible);

                        registeredPanels[panelId].visible = actualVisible;
                        discrepancies++;

                        // Update currentActivePanel if needed for IPC consistency
                        if (actualVisible && currentActivePanel !== panelId) {
                            // If panel is actually visible but not tracked as active, update it
                            currentActivePanel = panelId;
                            logIpc("Updated currentActivePanel to " + panelId + " for IPC consistency");
                        } else if (!actualVisible && currentActivePanel === panelId) {
                            // If panel is not visible but tracked as active, clear it
                            currentActivePanel = "";
                            logIpc("Cleared currentActivePanel for IPC consistency");
                        }
                    }
                } catch (e) {
                    logIpc("Error checking panel " + panelId + " visibility: " + e.message);
                }
            }
        }

        return discrepancies;
    }

    function cleanupOrphanedPanelData() {
        try {
            var panelIds = Object.keys(registeredPanels);
            var orphanedPanels = [];

            for (var i = 0; i < panelIds.length; i++) {
                var panelId = panelIds[i];
                var panelData = registeredPanels[panelId];

                // Check if panel object is still valid
                if (!panelData.object) {
                    orphanedPanels.push(panelId);
                    continue;
                }

                try {
                    // Try to access a property to see if object is still alive
                    var testAccess = panelData.object.visible;
                } catch (error) {
                    logIpc("Panel object no longer accessible: " + panelId);
                    orphanedPanels.push(panelId);
                }
            }

            // Clean up orphaned panels
            for (var j = 0; j < orphanedPanels.length; j++) {
                logIpc("Cleaning up orphaned panel data: " + orphanedPanels[j]);
                unregisterPanel(orphanedPanels[j]);
            }

            return orphanedPanels.length;
        } catch (error) {
            logIpc("Error during orphaned panel cleanup: " + error.message);
            return 0;
        }
    }

    function refreshPanelStates() {
        try {
            logIpc("Refreshing panel states for IPC accuracy");

            // First clean up any orphaned data
            var orphanedCount = cleanupOrphanedPanelData();

            // Then verify synchronization
            var discrepancyCount = verifySynchronization();

            // Validate all panels
            var invalidCount = validatePanels();

            logIpc("Panel state refresh complete - orphaned: " + orphanedCount + ", discrepancies: " + discrepancyCount + ", invalid: " + invalidCount);

            return {
                orphaned: orphanedCount,
                discrepancies: discrepancyCount,
                invalid: invalidCount
            };
        } catch (error) {
            logIpc("Error during panel state refresh: " + error.message);
            return {
                orphaned: 0,
                discrepancies: 0,
                invalid: 0
            };
        }
    }

    // IPC State Functions
    function getCalendarState() {
        try {
            var calendarPanel = registeredPanels["calendar"];
            if (!calendarPanel) {
                return JSON.stringify({
                    error: "Calendar panel not registered",
                    available: false,
                    timestamp: Date.now()
                });
            }

            var state = {
                panelId: "calendar",
                visible: calendarPanel.visible,
                lastShown: calendarPanel.lastShown,
                active: currentActivePanel === "calendar",
                configuration: {
                    width: 450,
                    height: 280,
                    position: "top-left"
                },
                metadata: {
                    type: "calendar",
                    capabilities: ["date-selection", "timer"]
                },
                timestamp: Date.now()
            };

            return JSON.stringify(state);
        } catch (error) {
            logIpc("Error getting calendar state: " + error.message);
            return JSON.stringify({
                error: "Failed to get calendar state: " + error.message,
                available: false,
                timestamp: Date.now()
            });
        }
    }

    function getNotificationState() {
        try {
            var notificationPanel = registeredPanels["notification"];
            if (!notificationPanel) {
                return JSON.stringify({
                    error: "Notification panel not registered",
                    available: false,
                    timestamp: Date.now()
                });
            }

            // Try to get notification count from the notification widget
            var notificationCount = 0;
            // First try to get it from the notification widget (which has the accurate count)
            if (typeof window !== "undefined" && window.notificationModuleItem && window.notificationModuleItem.notificationCount !== undefined) {
                notificationCount = window.notificationModuleItem.notificationCount;
            }
            // Fallback to trying the overlay's notification manager
            else if (notificationPanel.object && notificationPanel.object.item && notificationPanel.object.item.notificationManagerLoader && notificationPanel.object.item.notificationManagerLoader.item && notificationPanel.object.item.notificationManagerLoader.item.list) {
                notificationCount = notificationPanel.object.item.notificationManagerLoader.item.list.length;
            }

            var state = {
                panelId: "notification",
                visible: notificationPanel.visible,
                lastShown: notificationPanel.lastShown,
                active: currentActivePanel === "notification",
                configuration: {
                    width: 380,
                    height: "auto",
                    position: "top-right"
                },
                metadata: {
                    type: "notification",
                    capabilities: ["history", "clear-all"],
                    notificationCount: notificationCount
                },
                timestamp: Date.now()
            };

            return JSON.stringify(state);
        } catch (error) {
            logIpc("Error getting notification state: " + error.message);
            return JSON.stringify({
                error: "Failed to get notification state: " + error.message,
                available: false,
                timestamp: Date.now()
            });
        }
    }

    function getWallpaperState() {
        try {
            var wallpaperPanel = registeredPanels["wallpaper"];
            if (!wallpaperPanel) {
                return JSON.stringify({
                    error: "Wallpaper panel not registered",
                    available: false,
                    timestamp: Date.now()
                });
            }

            // Try to get wallpaper info from the panel object
            var wallpaperCount = 0;
            var currentWallpaper = "unknown";
            if (wallpaperPanel.object && wallpaperPanel.object.item) {
                if (wallpaperPanel.object.item.wallpaperRepeater) {
                    wallpaperCount = wallpaperPanel.object.item.wallpaperRepeater.count;
                }
            }

            var state = {
                panelId: "wallpaper",
                visible: wallpaperPanel.visible,
                lastShown: wallpaperPanel.lastShown,
                active: currentActivePanel === "wallpaper",
                configuration: {
                    width: 800,
                    height: 600,
                    position: "center"
                },
                metadata: {
                    type: "wallpaper",
                    capabilities: ["wallpaper-selection", "preview"],
                    wallpaperCount: wallpaperCount,
                    currentWallpaper: currentWallpaper
                },
                timestamp: Date.now()
            };

            return JSON.stringify(state);
        } catch (error) {
            logIpc("Error getting wallpaper state: " + error.message);
            return JSON.stringify({
                error: "Failed to get wallpaper state: " + error.message,
                available: false,
                timestamp: Date.now()
            });
        }
    }

    function getMprisState() {
        try {
            var mprisPanel = registeredPanels["mpris"];
            if (!mprisPanel) {
                return JSON.stringify({
                    error: "MPRIS panel not registered",
                    available: false,
                    timestamp: Date.now()
                });
            }

            // Try to get mpris info from the panel object
            var playerName = "none";
            var trackTitle = "unknown";
            var trackArtist = "unknown";
            var playbackState = "stopped";
            var canGoNext = false;
            var canGoPrevious = false;
            var canPlay = false;
            var canPause = false;

            if (mprisPanel.object && mprisPanel.object.item && mprisPanel.object.item.activePlayer) {
                var player = mprisPanel.object.item.activePlayer;
                playerName = player.identity || "unknown";
                trackTitle = player.trackTitle || "unknown";
                trackArtist = player.trackArtist || "unknown";
                // Convert playback state enum to string
                if (player.playbackState === 0)
                    playbackState = "stopped";
                else if (player.playbackState === 1)
                    playbackState = "playing";
                else if (player.playbackState === 2)
                    playbackState = "paused";
                canGoNext = player.canGoNext || false;
                canGoPrevious = player.canGoPrevious || false;
                canPlay = player.canPlay || false;
                canPause = player.canPause || false;
            }

            var state = {
                panelId: "mpris",
                visible: mprisPanel.visible,
                lastShown: mprisPanel.lastShown,
                active: currentActivePanel === "mpris",
                configuration: {
                    width: 400,
                    height: 200,
                    position: "center"
                },
                metadata: {
                    type: "mpris",
                    capabilities: ["play", "pause", "next", "previous"],
                    playerName: playerName,
                    trackTitle: trackTitle,
                    trackArtist: trackArtist,
                    playbackState: playbackState,
                    playerCapabilities: {
                        canGoNext: canGoNext,
                        canGoPrevious: canGoPrevious,
                        canPlay: canPlay,
                        canPause: canPause
                    }
                },
                timestamp: Date.now()
            };

            return JSON.stringify(state);
        } catch (error) {
            logIpc("Error getting mpris state: " + error.message);
            return JSON.stringify({
                error: "Failed to get mpris state: " + error.message,
                available: false,
                timestamp: Date.now()
            });
        }
    }

    function getAllPanelStates() {
        try {
            var allStates = {};
            var panelIds = Object.keys(registeredPanels);

            for (var i = 0; i < panelIds.length; i++) {
                var panelId = panelIds[i];
                if (panelId === "calendar") {
                    allStates[panelId] = JSON.parse(getCalendarState());
                } else if (panelId === "notification") {
                    allStates[panelId] = JSON.parse(getNotificationState());
                } else if (panelId === "wallpaper") {
                    allStates[panelId] = JSON.parse(getWallpaperState());
                } else if (panelId === "mpris") {
                    allStates[panelId] = JSON.parse(getMprisState());
                } else {
                    // Generic panel state
                    var panelData = registeredPanels[panelId];
                    allStates[panelId] = {
                        panelId: panelId,
                        visible: panelData.visible,
                        lastShown: panelData.lastShown,
                        active: currentActivePanel === panelId,
                        timestamp: Date.now()
                    };
                }
            }

            return JSON.stringify({
                currentActivePanel: currentActivePanel,
                totalPanels: panelIds.length,
                panels: allStates,
                timestamp: Date.now()
            });
        } catch (error) {
            logIpc("Error getting all panel states: " + error.message);
            return JSON.stringify({
                error: "Failed to get all panel states: " + error.message,
                timestamp: Date.now()
            });
        }
    }

    Component.onCompleted: {
        logIpc("IPC Panel Manager initialized");
    }
}
