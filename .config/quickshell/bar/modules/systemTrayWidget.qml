import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import Quickshell
import Quickshell.Widgets
import Quickshell.Services.SystemTray
import "../modules"

RowLayout {
    id: systemTrayWidget
    spacing: 2

    property var parentTheme: null
    property var theme: parentTheme

    Repeater {
        model: ScriptModel {
            values: {
                const items = [...SystemTray.items.values].filter((item) => {
                    return true 
                })
                return items
            }
        }

        MouseArea {
            id: delegate
            required property SystemTrayItem modelData
            property alias item: delegate.modelData
            
            Layout.fillHeight: true
            implicitWidth: icon.implicitWidth + 2
            implicitHeight: 16
            
            acceptedButtons: Qt.LeftButton | Qt.RightButton | Qt.MiddleButton
            hoverEnabled: true
            
            onClicked: event => {
                if (event.button == Qt.LeftButton) {
                    item.activate()
                } else if (event.button == Qt.MiddleButton) {
                    item.secondaryActivate()
                } else if (event.button == Qt.RightButton) {
                    menuAnchor.open()
                }
            }
            
            onWheel: event => {
                event.accepted = true
                const points = event.angleDelta.y / 120
                item.scroll(points, false)
            }
            
            IconImage {
                id: icon
                anchors.centerIn: parent
                source: item.icon
                implicitSize: 14
            }
            
            QsMenuAnchor {
                id: menuAnchor
                menu: item.menu
                anchor.window: delegate.QsWindow.window
                anchor.adjustment: PopupAdjustment.Flip
                anchor.onAnchoring: {
                    const window = delegate.QsWindow.window
                    const widgetRect = window.contentItem.mapFromItem(delegate, 0, delegate.height, delegate.width, delegate.height)
                    menuAnchor.anchor.rect = widgetRect
                }
            }
            
            scale: containsMouse ? 1.1 : 1.0
            Behavior on scale {
                NumberAnimation { duration: 150; easing.type: Easing.OutCubic }
            }
        }
    }
}
