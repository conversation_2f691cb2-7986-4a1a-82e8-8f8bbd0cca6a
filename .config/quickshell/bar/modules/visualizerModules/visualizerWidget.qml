import QtQuick
import QtQuick.Layouts
import Quickshell.Io
import ".."

Rectangle {
    id: visualizerWidget
    width: 100
    height: 18
    radius: 9
    color: "transparent"
    clip: false

    property var parentTheme: null
    property var theme: parentTheme
    property var debuggerInstance: null

    property bool isActive: true
    property bool isSilent: true
    property int silenceCounter: 0
    property var audioData: new Array(15).fill(0)
    property int frameTimeMs: Math.floor(1000 / 60)
    property int updateCounter: 0
    property bool isHovered: false
    property real cursorX: 0
    property real cursorY: 0


    MouseArea {
        anchors.fill: parent
        hoverEnabled: true
        onEntered: isHovered = true
        onExited: isHovered = false
        onPositionChanged: function(mouse) {
            cursorX = mouse.x
            cursorY = mouse.y
        }
    }

    Process {
        id: cavaProcess
        command: ["/sbin/cava", "-p", "/home/<USER>/.config/quickshell/bar/modules/visualizerModules/cavaConfig.conf"]
        running: isActive

        stdout: SplitParser {
            splitMarker: "\n"

            onRead: data => {
                processAudioData(data);
            }
        }

        onExited: {
            if (debuggerInstance) debuggerInstance.logVisualizer("Cava process exited with code: " + exitCode)
            if (isActive) {
                restartTimer.start();
            }
        }

        onStarted: {
            if (debuggerInstance) debuggerInstance.logVisualizer("Cava process started successfully")
        }
    }

    Timer {
        id: restartTimer
        interval: 1000
        onTriggered: {
            if (isActive && !cavaProcess.running) {
                if (debuggerInstance) debuggerInstance.logVisualizer("Restarting cava process...")
                cavaProcess.running = true;
            }
        }
    }

    Row {
        id: visualizerBars
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.bottom: parent.bottom
        anchors.bottomMargin: 1
        spacing: 1

        Repeater {
            id: barRepeater
            model: 15

            Rectangle {
                id: bar
                width: 5
                height: getBarHeight(index)
                color: theme ? theme.accent : "#3ddbd9"
                opacity: getBarOpacity(index)
                radius: 1
                anchors.bottom: parent.bottom


                property real glowIntensity: 0



                Behavior on height {
                    NumberAnimation {
                        duration: 50
                        easing.type: Easing.OutQuad
                    }
                }

                Behavior on opacity {
                    NumberAnimation {
                        duration: 80
                        easing.type: Easing.OutCubic
                    }
                }



                Behavior on glowIntensity {
                    NumberAnimation {
                        duration: 100
                        easing.type: Easing.OutCubic
                    }
                }

                function getBarHeight(barIndex) {
                    updateCounter;
                    let level = audioData[barIndex] || 0;

                    return Math.max(2, Math.min(16, level * 1.5 + 2));
                }

                function getBarOpacity(barIndex) {
                    updateCounter;
                    let level = audioData[barIndex] || 0;
                    let intensity = Math.min(1.0, level / 7.0);

                    if (isSilent && silenceCounter > 60) {
                        return 0.1 + Math.sin(Date.now() / 500) * 0.1;
                    }


                    let baseOpacity = 0.3 + (intensity * 0.7);


                    if (isHovered) {
                        let barX = (barIndex * 6) + 2.5;
                        let distanceFromCursor = Math.abs(barX - cursorX);
                        let maxDistance = 50;

                        let glowFactor = Math.max(0, 1 - (distanceFromCursor / maxDistance));
                        glowIntensity = glowFactor;
                        baseOpacity += glowFactor * 0.5;
                    } else {
                        glowIntensity = 0;
                    }

                    return Math.min(1.0, baseOpacity);
                }
            }
        }
    }

    function processAudioData(data) {
        if (!data || data.length === 0)
            return;
        let line = data.trim();
        if (line.length > 0) {
            let values = line.split(' ');
            let hasAudio = false;

            for (let i = 0; i < 15; i++) {
                let level = parseInt(values[i]) || 0;
                level = Math.min(7, Math.max(0, level));
                audioData[i] = level;

                if (level > 0) {
                    hasAudio = true;
                }
            }


            updateCounter++;

            isSilent = !hasAudio;
            if (!isSilent) {
                silenceCounter = 0;
            } else {
                silenceCounter++;
            }
        }
    }

    Component.onCompleted: {
        if (debuggerInstance) debuggerInstance.logVisualizer("Visualizer widget initialized")

        for (let i = 0; i < 15; i++) {
            audioData[i] = 0;
        }
    }

    Component.onDestruction: {
        if (debuggerInstance) debuggerInstance.logVisualizer("Visualizer widget destroyed")
        cavaProcess.running = false;
    }
}
