import QtQuick
import Quickshell
import Quickshell.Services.Notifications
import Quickshell.Wayland
import "../"

Item {
    id: notificationWidget
    width: indicator.width
    height: 18

    property bool hasNotifications: false
    property int notificationCount: 0
    property string currentUrgency: "normal"
    property var activePopups: []

    property var parentTheme: null
    property var theme: parentTheme || themeLoader.item

    Loader {
        id: themeLoader
        source: "../theme.qml"
    }

    property var notificationManager: null
    
    onNotificationManagerChanged: {
        if (notificationManager) {
            updateNotificationCount()
        }
    }


    Component {
        id: notificationPopupComponent

        PanelWindow {
            id: popup

            property var notification: null
            property int popupIndex: 0

            screen: notificationWidget.parent ? notificationWidget.parent.screen : null
            color: "transparent"

            width: 380
            height: 120

            anchors {
                right: true
                top: true
            }

            margins {
                right: 20
                top: 50 + (popupIndex * 140)
            }

            WlrLayershell.layer: WlrLayer.Overlay
            WlrLayershell.exclusiveZone: 0
            WlrLayershell.keyboardFocus: WlrKeyboardFocus.None

            visible: true

            Rectangle {
                anchors.fill: parent
                radius: theme ? theme.largeBorderRadius : 8
                color: theme ? theme.background : "#000000"


                Rectangle {
                    id: iconContainer
                    anchors.left: parent.left
                    anchors.top: parent.top
                    anchors.bottom: parent.bottom
                    anchors.margins: 16
                    width: height
                    color: "transparent"

                    Image {
                        id: appIcon
                        anchors.fill: parent
                        source: {
                            if (popup.notification && isScreenshotNotification()) {
                                var screenshotPath = extractScreenshotPath()
                                if (screenshotPath) {
                                    return "file://" + screenshotPath
                                }
                            }

                            if (popup.notification && popup.notification.appIcon && popup.notification.appIcon !== "") {
                                return popup.notification.appIcon
                            }
                            return "file://" + "/home/<USER>/.config/quickshell/bar/icons/mark-a.png"
                        }
                        fillMode: isScreenshotNotification() ? Image.PreserveAspectCrop : Image.PreserveAspectFit
                        smooth: true
                        cache: false

                        onStatusChanged: {
                            if (status === Image.Error) {
                                source = "file://" + "/home/<USER>/.config/quickshell/bar/icons/mark-a.png"
                            }
                        }
                        function isScreenshotNotification() {
                            if (!popup.notification) return false
                            var summary = popup.notification.summary || ""
                            var body = popup.notification.body || ""
                            var appName = popup.notification.appName || ""

                            return summary.toLowerCase().includes("screenshot") ||
                                   body.toLowerCase().includes("screenshot") ||
                                   appName.toLowerCase().includes("screenshot") ||
                                   summary.toLowerCase().includes("saved")
                        }

                        function extractScreenshotPath() {
                            if (!popup.notification) return ""
                            var body = popup.notification.body || ""


                            var pathMatch = body.match(/\/[^\s]+\.(png|jpg|jpeg)/i)
                            if (pathMatch) {
                                return pathMatch[0]
                            }


                            if (body.includes("/Pictures/screenshots/")) {
                                var screenshotMatch = body.match(/\/Pictures\/screenshots\/[^\s]+/i)
                                if (screenshotMatch) {
                                    return screenshotMatch[0]
                                }
                            }

                            return ""
                        }
                    }
                }


                Column {
                    anchors.left: iconContainer.right
                    anchors.right: parent.right
                    anchors.top: parent.top
                    anchors.bottom: parent.bottom
                    anchors.margins: 16
                    anchors.leftMargin: 12
                    spacing: 10

                    Text {
                        text: popup.notification ? popup.notification.summary : ""
                        color: theme ? theme.textPrimary : "#f2f4f8"
                        font.pixelSize: 14
                        font.weight: Font.Bold
                        wrapMode: Text.WordWrap
                        width: parent.width
                        maximumLineCount: 1
                        elide: Text.ElideRight
                    }

                    Text {
                        text: popup.notification ? popup.notification.body : ""
                        color: theme ? theme.textSecondary : "#999999"
                        font.pixelSize: 12
                        wrapMode: Text.WordWrap
                        width: parent.width
                        maximumLineCount: 3
                        elide: Text.ElideRight
                    }
                    Text {
                        text: popup.notification ? popup.notification.appName : ""
                        color: theme ? theme.textTertiary : "#777777"
                        font.pixelSize: 10
                        font.italic: true
                    }
                }


                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        if (popup.notification) {
                            popup.notification.dismiss()
                        }
                        removePopup(popup)
                    }
                }
            }

            // Auto-hide timer - maximum 1 second
            Timer {
                id: autoHideTimer
                interval: 3000  // Always 1 second max
                running: true
                repeat: false
                onTriggered: {
                    removePopup(popup)
                }
            }
        }
    }

    // Smart popup management
    property int maxPopupsOnScreen: 4

    function createNotificationPopup(notification) {
        if (activePopups.length >= maxPopupsOnScreen) {
            const oldestPopup = activePopups.shift()
            if (oldestPopup) {
                oldestPopup.destroy()
            }
        }

        const popup = notificationPopupComponent.createObject(notificationWidget, {
            "notification": notification,
            "popupIndex": activePopups.length
        })

        if (popup) {
            activePopups.push(popup)
            repositionPopups()
        }
    }

    // Always show indicator - gray circle when no notifications, colored when has notifications
    Rectangle {
        id: indicator
        width: content.width + 8
        height: 18
        radius: 9
        color: hasNotifications ? theme.accent : "transparent"
        border.width: hasNotifications ? 1 : 0
        border.color: hasNotifications ? theme.border : "transparent"

        Row {
            id: content
            anchors.centerIn: parent
            spacing: 4

            Text {
                text: hasNotifications ? "●" : "○"
                color: hasNotifications ? theme.background : theme.textSecondary
                font.pixelSize: 12
                font.family: "JetBrains Mono, monospace"
                anchors.verticalCenter: parent.verticalCenter
            }

            Text {
                text: notificationCount > 0 ? notificationCount.toString() : ""
                color: hasNotifications ? theme.background : theme.textPrimary
                font.pixelSize: 10
                font.family: "JetBrains Mono, monospace"
                anchors.verticalCenter: parent.verticalCenter
                visible: notificationCount > 0
            }
        }

        MouseArea {
            anchors.fill: parent
            acceptedButtons: Qt.LeftButton | Qt.RightButton

            onClicked: function(mouse) {
                if (mouse.button === Qt.LeftButton) {
                    showNotificationOverlay()
                } else if (mouse.button === Qt.RightButton) {
                    clearAllNotifications()
                }
            }
        }

        Behavior on width {
            NumberAnimation { duration: 200; easing.type: Easing.OutCubic }
        }

        Behavior on height {
            NumberAnimation { duration: 200; easing.type: Easing.OutCubic }
        }
    }

    // Notification overlay signal
    signal showNotificationOverlayRequested()
    signal hideNotificationOverlayRequested()

    function showNotificationOverlay() {
        showNotificationOverlayRequested()
    }

    function removePopup(popup) {
        const index = activePopups.indexOf(popup)
        if (index > -1) {
            activePopups.splice(index, 1)
            if (popup && popup.destroy) {
                popup.destroy()
            }
            repositionPopups()
    
        }
    }

    function repositionPopups() {
        for (let i = 0; i < activePopups.length; i++) {
            if (activePopups[i] && activePopups[i].margins) {
                activePopups[i].popupIndex = i
                // Update position for PanelWindow
                activePopups[i].margins.top = 50 + (i * 140)
            }
        }
    }

    function removeNotificationFromList(notificationId) {
        // Use the shared notification manager to remove notifications
        if (notificationManager) {
            notificationManager.discardNotification(notificationId)
        }
        updateNotificationCount()
    }

    function updateNotificationCount() {
        // Use the shared notification manager's list
        if (notificationManager && notificationManager.list) {
            notificationCount = notificationManager.list.length
            hasNotifications = notificationCount > 0

            // Update urgency based on notifications
            let highestUrgency = "low"
            const notifications = notificationManager.list
            for (let i = 0; i < notifications.length; i++) {
                const notification = notifications[i]
                if (notification.urgency === "critical") {
                    highestUrgency = "critical"
                    break
                } else if (notification.urgency === "normal" && highestUrgency === "low") {
                    highestUrgency = "normal"
                }
            }
            currentUrgency = highestUrgency
        } else {
            notificationCount = 0
            hasNotifications = false
            currentUrgency = "low"
        }


    }

    function clearAllNotifications() {
        if (notificationManager) {
            notificationManager.discardAllNotifications()
        }

        for (const popup of activePopups) {
            if (popup && popup.destroy) {
                popup.destroy()
            }
        }
        activePopups = []

        updateNotificationCount()
    }

    // Monitor notification manager changes
    Connections {
        target: notificationManager
        function onListChanged() {
            updateNotificationCount()
        }
        function onNotify(notification) {
            // Create popup for new notifications
            createNotificationPopup(notification)
        }
    }


}