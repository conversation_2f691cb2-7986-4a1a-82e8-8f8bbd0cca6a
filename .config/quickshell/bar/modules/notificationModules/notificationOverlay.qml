import QtQuick
import QtQuick.Layouts
import Quickshell
import Quickshell.Wayland
import "../"


PanelWindow {
    id: notificationOverlay
    
    property var screen: null
    property var barWindow: null
    property bool overlayVisible: false
    property var notificationWidget: null
    

    property var notificationManager: null
    property var parentTheme: null
    property var theme: parentTheme || themeLoader.item

    Loader {
        id: themeLoader
        source: "../theme.qml"
    }
    property var debuggerInstance: debuggerLoader.item

    signal overlayHidden()



    visible: overlayVisible
    color: "transparent"

    anchors {
        right: true
        top: true
        bottom: true
    }

    margins {
        right: 20
        top: 10
        bottom: 20
    }

    implicitWidth: 380
    focusable: true

    WlrLayershell.layer: WlrLayer.Overlay
    WlrLayershell.exclusiveZone: 0
    WlrLayershell.keyboardFocus: WlrKeyboardFocus.OnDemand



    Loader {
        id: debuggerLoader
        source: "../../debugger.qml"
    }


    

    Rectangle {
        id: backgroundRect
        anchors.fill: parent
        radius: theme.largeBorderRadius
        color: theme.background
        

        Grid {
            id: patternGrid
            anchors.fill: parent
            anchors.margins: theme.largeBorderRadius
            clip: true
            
            property int patternSize: 150
            property int tilesX: Math.ceil(parent.width / patternSize)
            property int tilesY: Math.ceil(parent.height / patternSize)
            
            columns: tilesX
            rows: tilesY
            
            Repeater {
                model: patternGrid.tilesX * patternGrid.tilesY
                
                Image {
                    width: patternGrid.patternSize
                    height: patternGrid.patternSize
                    source: "file:///home/<USER>/.config/quickshell/bar/icons/pattern-sign.png"
                    fillMode: Image.PreserveAspectFit
                    smooth: true
                    cache: true
                    opacity: 0.35
                    
                    onStatusChanged: {
                        if (status === Image.Error) {
                            visible = false
                        }
                    }
                }
            }
        }
        

        Rectangle {
            id: header
            anchors.top: parent.top
            anchors.left: parent.left
            anchors.right: parent.right
            height: 40
            radius: theme.largeBorderRadius
            color: theme.backgroundSecondary
            
            Rectangle {
                anchors.bottom: parent.bottom
                anchors.left: parent.left
                anchors.right: parent.right
                height: 12
                color: theme.backgroundSecondary
            }
            
            RowLayout {
                anchors.fill: parent
                anchors.margins: 12
                
                Text {
                    text: "Notifications"
                    color: theme.textPrimary
                    font.pixelSize: 14
                    font.weight: Font.Bold
                    Layout.fillWidth: true
                }
                
                Text {
                    text: notificationManager ? notificationManager.list.length.toString() : "0"
                    color: theme.textSecondary
                    font.pixelSize: 12
                    font.family: "JetBrains Mono, monospace"
                }
                
                Rectangle {
                    width: 60
                    height: 20
                    radius: 10
                    color: theme.accent
                    
                    Text {
                        anchors.centerIn: parent
                        text: "Clear"
                        color: theme.background
                        font.pixelSize: 10
                        font.weight: Font.Bold
                    }
                    
                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            notificationManager.discardAllNotifications()
                        }
                    }
                }
            }
        }

        ListView {
            id: notificationList
            anchors.top: header.bottom
            anchors.left: parent.left
            anchors.right: parent.right
            anchors.bottom: parent.bottom
            anchors.margins: 12
            anchors.topMargin: 8

            model: notificationManager ? notificationManager.list.slice(0, 10) : []
            

            spacing: 8
            clip: true

            delegate: Rectangle {
                width: notificationList.width
                height: 90
                radius: theme.borderRadius
                color: theme.backgroundSecondary
                border.width: 1
                border.color: theme.border


                Rectangle {
                    id: iconContainer
                    anchors.left: parent.left
                    anchors.top: parent.top
                    anchors.bottom: parent.bottom
                    anchors.margins: 12
                    width: height
                    color: "transparent"

                    Image {
                        id: appIcon
                        anchors.fill: parent
                        source: {
                            if (!modelData) return "file:///home/<USER>/.config/quickshell/bar/icons/mark-a.png"
                            

                            if (isScreenshotNotification()) {
                                var screenshotPath = extractScreenshotPath()
                                if (screenshotPath) {
                                    return "file://" + screenshotPath
                                }
                            }


                            if (modelData.image && modelData.image !== "") {
                                return modelData.image.startsWith("file://") ? modelData.image : "file://" + modelData.image
                            }
                            

                            if (modelData.appIcon && modelData.appIcon !== "") {
                                return modelData.appIcon.startsWith("file://") ? modelData.appIcon : "file://" + modelData.appIcon
                            }
                            

                            return "file:///home/<USER>/.config/quickshell/bar/icons/mark-a.png"
                        }
                        fillMode: isScreenshotNotification() ? Image.PreserveAspectCrop : Image.PreserveAspectFit
                        smooth: true
                        cache: false

                        onStatusChanged: {
                            if (status === Image.Error) {
                                source = "file://" + "/home/<USER>/.config/quickshell/bar/icons/mark-a.png"
                            }
                        }

                        function isScreenshotNotification() {
                            if (!modelData) return false
                            var summary = modelData.summary || ""
                            var body = modelData.body || ""
                            var appName = modelData.appName || ""

                            return summary.toLowerCase().includes("screenshot") ||
                                   body.toLowerCase().includes("screenshot") ||
                                   appName.toLowerCase().includes("screenshot") ||
                                   summary.toLowerCase().includes("saved")
                        }

                        function extractScreenshotPath() {
                            if (!modelData) return ""
                            var body = modelData.body || ""

                            var pathMatch = body.match(/\/[^\s]+\.(png|jpg|jpeg)/i)
                            if (pathMatch) {
                                return pathMatch[0]
                            }

                            if (body.includes("/Pictures/screenshots/")) {
                                var screenshotMatch = body.match(/\/Pictures\/screenshots\/[^\s]+/i)
                                if (screenshotMatch) {
                                    return screenshotMatch[0]
                                }
                            }

                            return ""
                        }
                    }
                }


                Column {
                    anchors.left: iconContainer.right
                    anchors.right: parent.right
                    anchors.top: parent.top
                    anchors.bottom: parent.bottom
                    anchors.margins: 12
                    anchors.leftMargin: 8
                    spacing: 4


                    Text {
                        text: modelData.summary || ""
                        color: theme.textPrimary
                        font.pixelSize: 12
                        font.weight: Font.Bold
                        wrapMode: Text.WordWrap
                        width: parent.width
                        maximumLineCount: 1
                        elide: Text.ElideRight
                    }


                    Text {
                        text: modelData.body || ""
                        color: theme.textSecondary
                        font.pixelSize: 10
                        wrapMode: Text.WordWrap
                        width: parent.width
                        maximumLineCount: 2
                        elide: Text.ElideRight
                    }


                    Text {
                        text: modelData.appName || ""
                        color: theme.textTertiary
                        font.pixelSize: 9
                        font.italic: true
                        elide: Text.ElideRight
                        width: parent.width
                    }
                }

                MouseArea {
                    anchors.fill: parent
                    onClicked: {

                        notificationManager.discardNotification(modelData.notificationId)
                    }
                }
            }


            Text {
                anchors.centerIn: parent
                text: "No notifications"
                color: theme.textSecondary
                font.pixelSize: 14
                visible: notificationList.count === 0
            }
        }
    }
    

    MouseArea {
        anchors.fill: parent
        propagateComposedEvents: true
        acceptedButtons: Qt.LeftButton | Qt.RightButton

        onClicked: function(mouse) {
            var clickX = mouse.x
            var clickY = mouse.y
            var contentLeft = backgroundRect.x
            var contentTop = backgroundRect.y
            var contentRight = backgroundRect.x + backgroundRect.width
            var contentBottom = backgroundRect.y + backgroundRect.height

            if (clickX < contentLeft || clickX > contentRight ||
                clickY < contentTop || clickY > contentBottom) {
                hideOverlay()
                mouse.accepted = true
            } else {
                mouse.accepted = false
            }
        }
    }
    
    function showOverlay() {
        overlayVisible = true
    }
    
    function hideOverlay() {
        overlayVisible = false
        overlayHidden()
    }
    
    function formatTime(timestamp) {
        const now = Date.now()
        const diff = now - timestamp
        const minutes = Math.floor(diff / 60000)
        const hours = Math.floor(minutes / 60)
        const days = Math.floor(hours / 24)
        
        if (days > 0) return days + "d"
        if (hours > 0) return hours + "h"
        if (minutes > 0) return minutes + "m"
        return "now"
    }
    

    Connections {
        target: notificationManager
        function onNotify(notification) {}
        function onDiscard(id) {}
        function onDiscardAll() {}
        function onListChanged() {
            notificationList.model = notificationManager.list.slice(0, 10)
        }
    }

}
