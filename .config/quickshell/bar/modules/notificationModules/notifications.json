[{"notificationId": 1, "actions": [], "appIcon": "/home/<USER>/Pictures/screenshots//2025-08-15-155313_hyprshot.png", "appName": "Hyprshot", "body": "Image saved in <i>/home/<USER>/Pictures/screenshots//2025-08-15-155313_hyprshot.png</i> and copied to the clipboard.", "image": "", "summary": "Screenshot saved", "time": 1755244395362, "urgency": "1"}, {"notificationId": 3, "actions": [], "appIcon": "/home/<USER>/Pictures/screenshots//2025-08-15-160345_hyprshot.png", "appName": "Hyprshot", "body": "Image saved in <i>/home/<USER>/Pictures/screenshots//2025-08-15-160345_hyprshot.png</i> and copied to the clipboard.", "image": "", "summary": "Screenshot saved", "time": 1755245027131, "urgency": "1"}, {"notificationId": 4, "actions": [], "appIcon": "", "appName": "notify-send", "body": "This is a test to verify syncing works", "image": "", "summary": "Test Notification", "time": 1755245114581, "urgency": "1"}, {"notificationId": 5, "actions": [], "appIcon": "/home/<USER>/Pictures/screenshots//2025-08-15-172616_hyprshot.png", "appName": "Hyprshot", "body": "Image saved in <i>/home/<USER>/Pictures/screenshots//2025-08-15-172616_hyprshot.png</i> and copied to the clipboard.", "image": "", "summary": "Screenshot saved", "time": 1755249979653, "urgency": "1"}, {"notificationId": 7, "actions": [], "appIcon": "zen-browser", "appName": "Zen", "body": "Disconnected", "image": "image://qsimage/5/1", "summary": "Proton VPN", "time": 1755252848110, "urgency": "1"}, {"notificationId": 8, "actions": [], "appIcon": "discord", "appName": "discord", "body": "<PERSON><PERSON><PERSON> invited you to play Marvel Rivals", "image": "image://qsimage/3/1", "summary": "<PERSON><PERSON><PERSON>", "time": 1755263861631, "urgency": "1"}, {"notificationId": 9, "actions": [], "appIcon": "", "appName": "<PERSON><PERSON>", "body": "Agent execution is waiting for your input", "image": "", "summary": "Agent Input Required", "time": 1755317652654, "urgency": "0"}, {"notificationId": 10, "actions": [], "appIcon": "", "appName": "<PERSON><PERSON>", "body": "Agent execution is waiting for your input", "image": "", "summary": "Agent Input Required", "time": 1755317792530, "urgency": "0"}, {"notificationId": 11, "actions": [], "appIcon": "", "appName": "notify-send", "body": "This is a test to verify syncing works", "image": "", "summary": "Test Notification", "time": 1755318462618, "urgency": "1"}, {"notificationId": 12, "actions": [], "appIcon": "", "appName": "notify-send", "body": "This is a test to verify syncing works", "image": "", "summary": "Test Notification", "time": 1755318463433, "urgency": "1"}, {"notificationId": 13, "actions": [], "appIcon": "", "appName": "notify-send", "body": "This is a test to verify syncing works", "image": "", "summary": "Test Notification", "time": 1755318463686, "urgency": "1"}, {"notificationId": 14, "actions": [], "appIcon": "", "appName": "notify-send", "body": "This is a test to verify syncing works", "image": "", "summary": "Test Notification", "time": 1755318463898, "urgency": "1"}, {"notificationId": 15, "actions": [], "appIcon": "", "appName": "notify-send", "body": "This is a test to verify syncing works", "image": "", "summary": "Test Notification", "time": 1755318580372, "urgency": "1"}, {"notificationId": 16, "actions": [], "appIcon": "", "appName": "notify-send", "body": "This is a test to verify syncing works", "image": "", "summary": "Test Notification", "time": 1755318581097, "urgency": "1"}, {"notificationId": 17, "actions": [], "appIcon": "", "appName": "notify-send", "body": "This is a test to verify syncing works", "image": "", "summary": "Test Notification", "time": 1755318581345, "urgency": "1"}, {"notificationId": 18, "actions": [{"identifier": "default", "text": "Activate"}], "appIcon": "zen-browser", "appName": "Zen", "body": "Connected to JP-FREE#18 in Tokyo", "image": "image://qsimage/1/1", "summary": "Proton VPN", "time": 1755324039088, "urgency": "1"}, {"notificationId": 19, "actions": [], "appIcon": "/home/<USER>/Pictures/screenshots//2025-08-16-164307_hyprshot.png", "appName": "Hyprshot", "body": "Image saved in <i>/home/<USER>/Pictures/screenshots//2025-08-16-164307_hyprshot.png</i> and copied to the clipboard.", "image": "", "summary": "Screenshot saved", "time": 1755333789188, "urgency": "1"}, {"notificationId": 20, "actions": [], "appIcon": "/home/<USER>/Pictures/screenshots//2025-08-16-164420_hyprshot.png", "appName": "Hyprshot", "body": "Image saved in <i>/home/<USER>/Pictures/screenshots//2025-08-16-164420_hyprshot.png</i> and copied to the clipboard.", "image": "", "summary": "Screenshot saved", "time": 1755333862471, "urgency": "1"}, {"notificationId": 21, "actions": [], "appIcon": "/home/<USER>/Pictures/screenshots//2025-08-16-164926_hyprshot.png", "appName": "Hyprshot", "body": "Image saved in <i>/home/<USER>/Pictures/screenshots//2025-08-16-164926_hyprshot.png</i> and copied to the clipboard.", "image": "", "summary": "Screenshot saved", "time": 1755334167824, "urgency": "1"}]