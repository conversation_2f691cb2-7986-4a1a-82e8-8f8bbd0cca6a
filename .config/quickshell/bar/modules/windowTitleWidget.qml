import QtQuick
import QtQuick.Layouts
import Quickshell.Io
import Quickshell.Hyprland
import "../"

Item {
    id: windowTitleContainer

    property int chopLength: 40
    property string activeWindowTitle: ""

    property var parentTheme: null
    property var theme: parentTheme

    width: titleRow.width
    height: 20

    Process {
        id: titleProc
        command: ["sh", "-c", "hyprctl activewindow | grep title: | sed 's/^[^:]*: //'"]
        running: true
        stdout: SplitParser {
            onRead: data => activeWindowTitle = data
        }
    }

    Component.onCompleted: {
        Hyprland.rawEvent.connect(hyprEvent)
    }

    function hyprEvent(e) {
        titleProc.running = true
    }

    Row {
        id: titleRow
        anchors.centerIn: parent
        spacing: 4

        Text {
            text: "『"
            color: theme ? theme.textAccent : "#3ddbd9"
            font.pixelSize: 16
            font.family: "JetBrains Mono Nerd Font, monospace"
            font.weight: Font.Bold
            anchors.verticalCenter: parent.verticalCenter
            opacity: activeWindowTitle.length > 0 ? 1.0 : 0.4

            Behavior on opacity {
                NumberAnimation {
                    duration: 400
                    easing.type: Easing.OutCubic
                }
            }
        }

        Text {
            id: titleText
            text: {
                var str = activeWindowTitle
                return str.length > chopLength ? str.slice(0, chopLength) + '...' : str
            }
            color: theme ? theme.textPrimary : "#f2f4f8"
            font.pixelSize: 12
            font.family: "JetBrains Mono, monospace"
            anchors.verticalCenter: parent.verticalCenter
            opacity: activeWindowTitle.length > 0 ? 1.0 : 0.6

            Behavior on opacity {
                NumberAnimation {
                    duration: 300
                    easing.type: Easing.OutCubic
                }
            }

            Behavior on text {
                SequentialAnimation {
                    NumberAnimation {
                        target: titleText
                        property: "opacity"
                        to: 0.3
                        duration: 150
                    }
                    NumberAnimation {
                        target: titleText
                        property: "opacity"
                        to: 1.0
                        duration: 150
                    }
                }
            }
        }

        Text {
            text: "』"
            color: theme ? theme.textAccent : "#3ddbd9"
            font.pixelSize: 16
            font.family: "JetBrains Mono Nerd Font, monospace"
            font.weight: Font.Bold
            anchors.verticalCenter: parent.verticalCenter
            opacity: activeWindowTitle.length > 0 ? 1.0 : 0.4

            Behavior on opacity {
                NumberAnimation {
                    duration: 200
                    easing.type: Easing.OutCubic
                }
            }
        }
    }
}