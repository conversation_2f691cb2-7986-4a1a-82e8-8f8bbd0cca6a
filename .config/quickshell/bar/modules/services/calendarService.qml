import QtQuick

QtObject {
    id: calendarService

    // Required properties from bar.qml
    property var parentTheme: null
    property var targetScreen: null
    property var debuggerInstance: null
    property var ipcPanelManager: null

    // Service-specific widget/overlay properties
    property var widget: null  // Will be set by bar.qml when it loads the widget
    property var overlay: calendarOverlayLoader
    property var overlayItem: calendarOverlayLoader.item

    // State properties
    property bool overlayVisible: ipcPanelManager ? ipcPanelManager.getCurrentActivePanel() === "calendar" : false

    // Calendar overlay loader
    property var calendarOverlayLoader: Loader {
        source: "../calendarModules/calendarOverlay.qml"
        onLoaded: {
            if (item) {
                item.screen = targetScreen;
                if (item.hasOwnProperty("parentTheme")) {
                    item.parentTheme = parentTheme;
                }
                item.overlayVisible = Qt.binding(function () {
                    return ipcPanelManager ? ipcPanelManager.getCurrentActivePanel() === "calendar" : false;
                });
                item.overlayHidden.connect(function () {
                    if (ipcPanelManager)
                        ipcPanelManager.hidePanel("calendar");
                });
            }
        }
    }

    // Function to set up widget connections when widget is loaded by bar.qml
    function setupWidget(widgetItem) {
        console.log("calendarService: setupWidget called with widget:", !!widgetItem);
        widget = widgetItem;
        if (widget && parentTheme) {
            widget.parentTheme = parentTheme;
        }
        // Set up connections - will be called again when ipcPanelManager becomes available
        connectWidgetSignals();
    }

    function connectWidgetSignals() {
        if (!widget || !ipcPanelManager) {
            console.log("calendarService: Cannot connect widget signals - widget:", !!widget, "ipcPanelManager:", !!ipcPanelManager);
            return;
        }

        console.log("calendarService: Connecting widget signals");

        // Connect calendar overlay show/hide signals
        if (widget.hasOwnProperty("showCalendarOverlayRequested")) {
            widget.showCalendarOverlayRequested.connect(function () {
                console.log("calendarService: showCalendarOverlayRequested signal received");
                if (ipcPanelManager.getCurrentActivePanel() === "calendar") {
                    ipcPanelManager.hidePanel("calendar");
                } else {
                    ipcPanelManager.showPanel("calendar");
                }
            });
        }
        if (widget.hasOwnProperty("hideCalendarOverlayRequested")) {
            widget.hideCalendarOverlayRequested.connect(function () {
                console.log("calendarService: hideCalendarOverlayRequested signal received");
                if (ipcPanelManager)
                    ipcPanelManager.hidePanel("calendar");
            });
        }
    }

    // Service interface functions
    function showOverlay() {
        if (ipcPanelManager) {
            ipcPanelManager.showPanel("calendar");
        }
    }

    function hideOverlay() {
        if (ipcPanelManager) {
            ipcPanelManager.hidePanel("calendar");
        }
    }

    function toggleOverlay() {
        if (ipcPanelManager) {
            ipcPanelManager.togglePanel("calendar");
        }
    }

    Component.onCompleted: {
        console.log("calendarService: Service initialized");
    }

    // Register with IPC manager when ipcPanelManager becomes available
    onIpcPanelManagerChanged: {
        if (ipcPanelManager) {
            Qt.callLater(function () {
                if (overlay) {
                    ipcPanelManager.registerPanel("calendar", overlay);
                    if (debuggerInstance) {
                        debuggerInstance.logNotification("calendarService: Registered with IPC manager");
                    }
                }
                // Also connect widget signals if widget is already available
                connectWidgetSignals();
            });
        }
    }

    // Also register when overlay becomes available
    onOverlayChanged: {
        if (ipcPanelManager && overlay) {
            Qt.callLater(function () {
                ipcPanelManager.registerPanel("calendar", overlay);
                if (debuggerInstance) {
                    debuggerInstance.logNotification("calendarService: Registered overlay with IPC manager");
                }
            });
        }
    }
}
