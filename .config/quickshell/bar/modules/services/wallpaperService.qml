import QtQuick
import Quickshell.Io
import "../wallpaperModules" as WallpaperModules

Item {
    id: wallpaperService
    
    // Required properties from bar.qml
    property var parentTheme: null
    property var targetScreen: null
    property var debuggerInstance: null
    property var ipcPanelManager: null
    
    // Service-specific widget/carousel properties
    property var widget: null  // Widget is loaded by bar.qml
    property var carousel: wallpaperCarouselLoader.item
    
    // State properties
    property bool overlayVisible: ipcPanelManager ? ipcPanelManager.getCurrentActivePanel() === "wallpaper" : false
    
    // Wallpaper carousel loader
    Loader {
        id: wallpaperCarouselLoader
        source: "../wallpaperModules/wallpaperCarousel.qml"
        active: true
        onLoaded: {
            if (debuggerInstance)
                debuggerInstance.logNotification("WallpaperCarousel Loader: Component loaded successfully");
            if (item) {
                if (debuggerInstance)
                    debuggerInstance.logNotification("WallpaperCarousel Loader: Item created successfully");
                item.screen = targetScreen;
                if (item.hasOwnProperty("parentTheme")) {
                    item.parentTheme = parentTheme;
                }
                // Integrate with ipcPanelManager
                item.overlayVisible = Qt.binding(function () {
                    return ipcPanelManager ? ipcPanelManager.getCurrentActivePanel() === "wallpaper" : false;
                });
                if (item.hasOwnProperty("overlayHidden")) {
                    item.overlayHidden.connect(function () {
                        if (ipcPanelManager)
                            ipcPanelManager.hidePanel("wallpaper");
                    });
                }
                // Widget will be connected by bar.qml when it loads
            } else {
                if (debuggerInstance)
                    debuggerInstance.logNotification("WallpaperCarousel Loader: Item is null after loading");
            }
        }
        onStatusChanged: {
            if (debuggerInstance)
                debuggerInstance.logNotification("WallpaperCarousel Loader status: " + status);
            if (status === Loader.Error) {
                if (debuggerInstance)
                    debuggerInstance.logNotification("WallpaperCarousel Loader ERROR: " + sourceComponent.errorString());
            }
        }
    }
    

    
    // Service interface functions
    function showOverlay() {
        if (ipcPanelManager) {
            ipcPanelManager.showPanel("wallpaper");
        }
    }
    
    function hideOverlay() {
        if (ipcPanelManager) {
            ipcPanelManager.hidePanel("wallpaper");
        }
    }
    
    function toggleOverlay() {
        if (ipcPanelManager) {
            ipcPanelManager.togglePanel("wallpaper");
        }
    }
    
    Component.onCompleted: {
        console.log("wallpaperService: Service initialized")
        
        // Register with ipcPanelManager when it becomes available
        Qt.callLater(function() {
            if (ipcPanelManager && carousel) {
                ipcPanelManager.registerPanel("wallpaper", carousel);
                ipcPanelManager.logIpc("WallpaperCarousel registered with IPC service");
                if (typeof WallpaperModules.WallpaperService !== "undefined") {
                    WallpaperModules.WallpaperService.loadWallpapers();
                    ipcPanelManager.logIpc("WallpaperService initialized");
                } else {
                    ipcPanelManager.logIpc("WallpaperService not available");
                }
            }
        });
    }
}