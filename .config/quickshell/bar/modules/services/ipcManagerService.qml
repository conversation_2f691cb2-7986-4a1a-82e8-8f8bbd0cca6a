import QtQuick
import Quickshell.Io

Item {
    id: ipcManagerService
    
    // Required properties from bar.qml
    property var parentTheme: null
    property var targetScreen: null
    property var debuggerInstance: null
    
    // IPC manager property - expose the loaded ipcPanelManager
    property var ipcManager: ipcPanelManagerLoader.item
    
    // IPC Panel Manager loader
    Loader {
        id: ipcPanelManagerLoader
        source: "../ipcPanelManager.qml"
    }
    
    // IPC Handler for MPRIS control
    IpcHandler {
        target: "mpris"

        function toggle(): void {
            if (debuggerInstance)
                debuggerInstance.logNotification("IPC: MPRIS toggle called");
            if (ipcManager) {
                ipcManager.togglePanel("mpris");
            }
        }

        function show(): void {
            if (debuggerInstance)
                debuggerInstance.logNotification("IPC: MPRIS show called");
            if (ipcManager) {
                ipcManager.showPanel("mpris");
            }
        }

        function hide(): void {
            if (debuggerInstance)
                debuggerInstance.logNotification("IPC: MPRIS hide called");
            if (ipcManager) {
                ipcManager.hidePanel("mpris");
            }
        }

        function isVisible(): bool {
            // Get visibility from the registered mpris panel
            if (ipcManager && ipcManager.registeredPanels && ipcManager.registeredPanels["mpris"]) {
                return ipcManager.registeredPanels["mpris"].visible;
            }
            return false;
        }

        function playPause(): void {
            if (debuggerInstance)
                debuggerInstance.logNotification("IPC: MPRIS playPause called");
            // Access the active player through the registered mpris panel
            if (ipcManager && ipcManager.registeredPanels && ipcManager.registeredPanels["mpris"]) {
                var mprisPanel = ipcManager.registeredPanels["mpris"];
                if (mprisPanel.object && mprisPanel.object.item && mprisPanel.object.item.activePlayer) {
                    mprisPanel.object.item.activePlayer.togglePlaying();
                }
            }
        }

        function next(): void {
            if (ipcManager && ipcManager.registeredPanels && ipcManager.registeredPanels["mpris"]) {
                var mprisPanel = ipcManager.registeredPanels["mpris"];
                if (mprisPanel.object && mprisPanel.object.item && mprisPanel.object.item.activePlayer && mprisPanel.object.item.activePlayer.canGoNext) {
                    mprisPanel.object.item.activePlayer.next();
                }
            }
        }

        function previous(): void {
            if (ipcManager && ipcManager.registeredPanels && ipcManager.registeredPanels["mpris"]) {
                var mprisPanel = ipcManager.registeredPanels["mpris"];
                if (mprisPanel.object && mprisPanel.object.item && mprisPanel.object.item.activePlayer && mprisPanel.object.item.activePlayer.canGoPrevious) {
                    mprisPanel.object.item.activePlayer.previous();
                }
            }
        }

        function getState(): string {
            return ipcManager ? ipcManager.getMprisState() : "unknown";
        }
    }

    // IPC Handler for calendar control
    IpcHandler {
        target: "calendar"

        function toggle(): void {
            if (debuggerInstance)
                debuggerInstance.logNotification("IPC: Calendar toggle called");
            if (ipcManager) {
                ipcManager.togglePanel("calendar");
            }
        }

        function show(): void {
            if (debuggerInstance)
                debuggerInstance.logNotification("IPC: Calendar show called");
            if (ipcManager) {
                ipcManager.showPanel("calendar");
            }
        }

        function hide(): void {
            if (debuggerInstance)
                debuggerInstance.logNotification("IPC: Calendar hide called");
            if (ipcManager) {
                ipcManager.hidePanel("calendar");
            }
        }

        function isVisible(): bool {
            // Get visibility from the registered calendar panel
            if (ipcManager && ipcManager.registeredPanels && ipcManager.registeredPanels["calendar"]) {
                return ipcManager.registeredPanels["calendar"].visible;
            }
            return false;
        }

        function getState(): string {
            return ipcManager ? ipcManager.getCalendarState() : "unknown";
        }
    }

    // IPC Handler for notification control
    IpcHandler {
        target: "notification"

        function toggle(): void {
            if (debuggerInstance)
                debuggerInstance.logNotification("IPC: Notification toggle called");
            if (ipcManager) {
                ipcManager.togglePanel("notification");
            }
        }

        function show(): void {
            if (debuggerInstance)
                debuggerInstance.logNotification("IPC: Notification show called");
            if (ipcManager) {
                ipcManager.showPanel("notification");
            }
        }

        function hide(): void {
            if (debuggerInstance)
                debuggerInstance.logNotification("IPC: Notification hide called");
            if (ipcManager) {
                ipcManager.hidePanel("notification");
            }
        }

        function isVisible(): bool {
            // Get visibility from the registered notification panel
            if (ipcManager && ipcManager.registeredPanels && ipcManager.registeredPanels["notification"]) {
                return ipcManager.registeredPanels["notification"].visible;
            }
            return false;
        }

        function getState(): string {
            return ipcManager ? ipcManager.getNotificationState() : "unknown";
        }
    }

    // IPC Handler for wallpaper control
    IpcHandler {
        target: "wallpaper"

        function toggle(): void {
            if (debuggerInstance)
                debuggerInstance.logNotification("IPC: Wallpaper toggle called");
            if (ipcManager) {
                ipcManager.togglePanel("wallpaper");
            }
        }

        function show(): void {
            if (debuggerInstance)
                debuggerInstance.logNotification("IPC: Wallpaper show called");
            if (ipcManager) {
                ipcManager.showPanel("wallpaper");
            }
        }

        function hide(): void {
            if (debuggerInstance)
                debuggerInstance.logNotification("IPC: Wallpaper hide called");
            if (ipcManager) {
                ipcManager.hidePanel("wallpaper");
            }
        }

        function isVisible(): bool {
            // Get visibility from the registered wallpaper panel
            if (ipcManager && ipcManager.registeredPanels && ipcManager.registeredPanels["wallpaper"]) {
                return ipcManager.registeredPanels["wallpaper"].visible;
            }
            return false;
        }

        function getState(): string {
            return ipcManager ? ipcManager.getWallpaperState() : "unknown";
        }
    }
    
    // Service interface functions for other services to use
    function registerPanel(panelId, panelObject) {
        if (ipcManager) {
            return ipcManager.registerPanel(panelId, panelObject);
        }
        console.log("ipcManagerService: registerPanel called for", panelId, "but ipcManager not available yet")
        return false;
    }
    
    function showPanel(panelId) {
        if (ipcManager) {
            return ipcManager.showPanel(panelId);
        }
        console.log("ipcManagerService: showPanel called for", panelId, "but ipcManager not available yet")
        return false;
    }
    
    function hidePanel(panelId) {
        if (ipcManager) {
            return ipcManager.hidePanel(panelId);
        }
        console.log("ipcManagerService: hidePanel called for", panelId, "but ipcManager not available yet")
        return false;
    }
    
    function togglePanel(panelId) {
        if (ipcManager) {
            return ipcManager.togglePanel(panelId);
        }
        console.log("ipcManagerService: togglePanel called for", panelId, "but ipcManager not available yet")
        return false;
    }
    
    function hideAllPanels() {
        if (ipcManager) {
            return ipcManager.hideAllPanels();
        }
        return 0;
    }
    
    function getCurrentActivePanel() {
        if (ipcManager) {
            return ipcManager.getCurrentActivePanel();
        }
        return "";
    }
    
    Component.onCompleted: {
        console.log("ipcManagerService: Service initialized")
    }
}