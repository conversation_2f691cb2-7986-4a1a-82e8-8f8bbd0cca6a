import QtQuick

Item {
    id: mprisService
    
    // Required properties from bar.qml
    property var parentTheme: null
    property var targetScreen: null
    property var debuggerInstance: null
    property var ipcPanelManager: null
    
    // Service-specific widget/overlay properties
    property var widget: mprisWidgetLoader.item
    property var overlay: mprisOverlayLoader
    property var overlayItem: mprisOverlayLoader.item
    
    // State properties
    property bool overlayVisible: ipcPanelManager ? ipcPanelManager.getCurrentActivePanel() === "mpris" : false
    
    // Widget loader
    Loader {
        id: mprisWidgetLoader
        source: "../mprisModules/mprisWidget.qml"
        onLoaded: {
            if (item && parentTheme) {
                item.parentTheme = parentTheme
            }
            if (item && debuggerInstance) {
                item.debuggerInstance = debuggerInstance
            }
        }
    }
    
    // Overlay loader
    Loader {
        id: mprisOverlayLoader
        source: "../mprisModules/mprisOverlay.qml"
        active: true
        onLoaded: {
            if (debuggerInstance)
                debuggerInstance.logNotification("MprisOverlay Loader: Component loaded successfully");
            if (item) {
                if (debuggerInstance)
                    debuggerInstance.logNotification("MprisOverlay Loader: Item created successfully");
                item.screen = targetScreen;
                if (item.hasOwnProperty("parentTheme")) {
                    item.parentTheme = parentTheme;
                }
                if (item.hasOwnProperty("debuggerInstance")) {
                    item.debuggerInstance = debuggerInstance;
                }
                // Integrate with ipcPanelManager
                item.overlayVisible = Qt.binding(function () {
                    return ipcPanelManager ? ipcPanelManager.getCurrentActivePanel() === "mpris" : false;
                });
                if (item.hasOwnProperty("overlayHidden")) {
                    item.overlayHidden.connect(function () {
                        if (ipcPanelManager)
                            ipcPanelManager.hidePanel("mpris");
                    });
                }
            } else {
                if (debuggerInstance)
                    debuggerInstance.logNotification("MprisOverlay Loader: Item is null after loading");
            }
        }
        onStatusChanged: {
            if (debuggerInstance)
                debuggerInstance.logNotification("MprisOverlay Loader status: " + status);
            if (status === Loader.Error) {
                if (debuggerInstance)
                    debuggerInstance.logNotification("MprisOverlay Loader ERROR: " + sourceComponent.errorString());
            }
        }
    }
    
    // Service interface functions
    function showOverlay() {
        if (ipcPanelManager) {
            ipcPanelManager.showPanel("mpris")
        }
    }
    
    function hideOverlay() {
        if (ipcPanelManager) {
            ipcPanelManager.hidePanel("mpris")
        }
    }
    
    function toggleOverlay() {
        if (ipcPanelManager) {
            ipcPanelManager.togglePanel("mpris")
        }
    }
    
    Component.onCompleted: {
        console.log("mprisService: Service initialized")
        console.log("mprisService: ipcPanelManager available:", !!ipcPanelManager)
        console.log("mprisService: overlay available:", !!overlay)
    }
    
    // Register with IPC manager when ipcPanelManager becomes available
    onIpcPanelManagerChanged: {
        if (ipcPanelManager) {
            Qt.callLater(function() {
                if (overlay) {
                    ipcPanelManager.registerPanel("mpris", overlay);
                    if (debuggerInstance) {
                        debuggerInstance.logNotification("mprisService: Registered with IPC manager");
                    }
                }
            })
        }
    }
    
    // Also register when overlay becomes available
    onOverlayChanged: {
        if (ipcPanelManager && overlay) {
            Qt.callLater(function() {
                ipcPanelManager.registerPanel("mpris", overlay);
                if (debuggerInstance) {
                    debuggerInstance.logNotification("mprisService: Registered overlay with IPC manager");
                }
            })
        }
    }
}