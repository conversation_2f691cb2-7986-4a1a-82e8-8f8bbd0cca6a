import QtQuick

Item {
    id: notificationService
    
    // Required properties from bar.qml
    property var parentTheme: null
    property var targetScreen: null
    property var debuggerInstance: null
    property var ipcPanelManager: null
    
    // Service-specific widget/overlay/manager properties
    property var widget: notificationWidgetLoader.item
    property var overlay: notificationOverlayLoader
    property var overlayItem: notificationOverlayLoader.item
    property var manager: notificationManagerLoader.item
    
    // State properties
    property bool overlayVisible: ipcPanelManager ? ipcPanelManager.getCurrentActivePanel() === "notification" : false
    
    // Notification manager loader
    Loader {
        id: notificationManagerLoader
        source: "../notificationModules/notificationManager.qml"
    }
    
    // Widget loader
    Loader {
        id: notificationWidgetLoader
        source: "../notificationModules/notificationWidget.qml"
        onLoaded: {
            if (item && parentTheme) {
                item.parentTheme = parentTheme
            }
            // Connect manager when both are available
            Qt.callLater(function() {
                if (item && manager) {
                    item.notificationManager = manager
                }
            })
            // Connect overlay show/hide signals
            if (item && item.hasOwnProperty("showNotificationOverlayRequested")) {
                item.showNotificationOverlayRequested.connect(function () {
                    if (ipcPanelManager && ipcPanelManager.getCurrentActivePanel() === "notification") {
                        ipcPanelManager.hidePanel("notification");
                    } else if (ipcPanelManager) {
                        ipcPanelManager.showPanel("notification");
                    }
                });
            }
            if (item && item.hasOwnProperty("hideNotificationOverlayRequested")) {
                item.hideNotificationOverlayRequested.connect(function () {
                    if (ipcPanelManager)
                        ipcPanelManager.hidePanel("notification");
                });
            }
        }
    }
    
    // Overlay loader
    Loader {
        id: notificationOverlayLoader
        source: "../notificationModules/notificationOverlay.qml"
        onLoaded: {
            if (item) {
                item.screen = targetScreen;
                if (item.hasOwnProperty("parentTheme")) {
                    item.parentTheme = parentTheme;
                }
                // Pass the notification manager instance
                if (item.hasOwnProperty("notificationManager")) {
                    item.notificationManager = manager;
                }
                item.overlayVisible = Qt.binding(function () {
                    return ipcPanelManager ? ipcPanelManager.getCurrentActivePanel() === "notification" : false;
                });
                item.notificationWidget = widget;
                item.overlayHidden.connect(function () {
                    if (ipcPanelManager)
                        ipcPanelManager.hidePanel("notification");
                });
            }
        }
    }
    
    // Service interface functions
    function showOverlay() {
        if (ipcPanelManager) {
            ipcPanelManager.showPanel("notification")
        }
    }
    
    function hideOverlay() {
        if (ipcPanelManager) {
            ipcPanelManager.hidePanel("notification")
        }
    }
    
    function toggleOverlay() {
        if (ipcPanelManager) {
            ipcPanelManager.togglePanel("notification")
        }
    }
    
    Component.onCompleted: {
        console.log("notificationService: Service initialized")
    }
    
    // Register with IPC manager when ipcPanelManager becomes available
    onIpcPanelManagerChanged: {
        if (ipcPanelManager) {
            Qt.callLater(function() {
                if (overlay) {
                    ipcPanelManager.registerPanel("notification", overlay);
                    if (debuggerInstance) {
                        debuggerInstance.logNotification("notificationService: Registered with IPC manager");
                    }
                }
            })
        }
    }
    
    // Also register when overlay becomes available
    onOverlayChanged: {
        if (ipcPanelManager && overlay) {
            Qt.callLater(function() {
                ipcPanelManager.registerPanel("notification", overlay);
                if (debuggerInstance) {
                    debuggerInstance.logNotification("notificationService: Registered overlay with IPC manager");
                }
            })
        }
    }
}