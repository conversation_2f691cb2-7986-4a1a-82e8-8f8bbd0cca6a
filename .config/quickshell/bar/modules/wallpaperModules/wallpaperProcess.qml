import QtQuick
import Quickshell.Io
import "../"

Process {
    id: wallpaperProcess
    
    command: ["sh", "-c", "find /home/<USER>/.git/dots/Pictures/Wallpaper -type f \\( -iname '*.jpg' -o -iname '*.png' -o -iname '*.jpeg' \\) | sort"]
    running: true
    
    stdout: StdioCollector {
        id: stdoutCollector
    }
    
    onExited: function(exitCode, exitStatus) {
        if (exitCode === 0) {
            WallpaperService.processWallpaperList(stdoutCollector.text)
        }
        destroy()
    }
}