import QtQuick
import Quickshell.Io
import "../"

Process {
    id: wallpaperApply

    property string wallpaperPath: ""
    property var transitions: ["simple", "fade", "left", "right", "top", "bottom", "wipe", "grow", "center", "outer", "random", "wave"]
    property string randomTransition: transitions[Math.floor(Math.random() * transitions.length)]

    command: ["sh", "-c", "if ! pgrep -x swww-daemon > /dev/null; then swww init; fi; swww img '" + wallpaperPath + "' --transition-type " + randomTransition + " --transition-duration 1"]
    running: true

    onExited: function (exitCode, exitStatus) {
        destroy();
    }
}
