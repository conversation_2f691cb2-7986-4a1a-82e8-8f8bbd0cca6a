pragma Singleton
import QtQuick
import Quickshell
import Quickshell.Io
import "../"

Singleton {
    id: wallpaperService

    property var wallpapers: []
    property string wallpaperDir: "/home/<USER>/.git/dots/Pictures/Wallpaper"
    property string configPath: "/home/<USER>/.config/quickshell/bar/modules/wallpaperModules/wallpapers.json"
    property int currentIndex: 0
    property int carouselStartIndex: 0
    property bool isLoaded: false
    property var currentVisibleWallpapers: []

    Component.onCompleted: {
        loadWallpapers();
    }

    function loadWallpapers() {
        var processComponent = Qt.createComponent("wallpaperProcess.qml");
        if (processComponent.status === Component.Ready) {
            var processObj = processComponent.createObject(wallpaperService);
        }
    }

    function processWallpaperList(output) {
        if (!output || output === null) {
            return;
        }

        var lines = output.trim().split('\n');
        var wallpaperList = [];

        for (var i = 0; i < lines.length; i++) {
            var path = lines[i].trim();
            if (path.length > 0) {
                var fileName = path.split('/').pop();
                var baseName = fileName.replace(/\.[^/.]+$/, "");

                wallpaperList.push({
                    path: path,
                    name: baseName,
                    displayName: baseName.replace(/_/g, " ")
                });
            }
        }

        wallpapers = wallpaperList;
    }

    function nextWallpaper() {
        if (wallpapers.length === 0)
            return;
        currentVisibleWallpapers = [];
        carouselStartIndex = (carouselStartIndex + 1) % wallpapers.length;
    }

    function previousWallpaper() {
        if (wallpapers.length === 0)
            return;
        currentVisibleWallpapers = [];
        carouselStartIndex = carouselStartIndex === 0 ? wallpapers.length - 1 : carouselStartIndex - 1;
    }

    function refreshVisibleWallpapers() {
        currentVisibleWallpapers = [];
    }

    function getVisibleWallpapers() {
        if (currentVisibleWallpapers.length === 3) {
            return currentVisibleWallpapers;
        }

        return generateNewVisibleWallpapers();
    }

    function generateNewVisibleWallpapers() {
        if (wallpapers.length === 0)
            return [];

        var visible = [];
        var usedIndices = [];

        for (var i = 0; i < 3 && i < wallpapers.length; i++) {
            var randomIndex;
            do {
                randomIndex = Math.floor(Math.random() * wallpapers.length);
            } while (usedIndices.indexOf(randomIndex) !== -1)

            usedIndices.push(randomIndex);
            visible.push(wallpapers[randomIndex]);
        }

        currentVisibleWallpapers = visible;
        return visible;
    }

    function getRandomWallpapers(count) {
        if (wallpapers.length === 0)
            return [];

        var randomWallpapers = [];
        var usedIndices = [];

        for (var i = 0; i < count && i < wallpapers.length; i++) {
            var randomIndex;
            do {
                randomIndex = Math.floor(Math.random() * wallpapers.length);
            } while (usedIndices.indexOf(randomIndex) !== -1)

            usedIndices.push(randomIndex);
            randomWallpapers.push(wallpapers[randomIndex]);
        }

        return randomWallpapers;
    }

    function applyWallpaper(wallpaperPath) {
        var applyComponent = Qt.createComponent("wallpaperApply.qml");
        if (applyComponent.status === Component.Ready) {
            var applyObj = applyComponent.createObject(wallpaperService, {
                "wallpaperPath": wallpaperPath
            });
        }
    }
}
