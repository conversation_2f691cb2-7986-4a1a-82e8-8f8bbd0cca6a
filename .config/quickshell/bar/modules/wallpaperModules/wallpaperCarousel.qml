import QtQuick
import QtQuick.Layouts
import Quickshell
import Quickshell.Wayland
import Quickshell.Io
import "../"
import "."
import "." as WallpaperModules

PanelWindow {
    id: wallpaperCarousel

    property var screen: null
    property bool overlayVisible: false

    signal overlayHidden

    visible: overlayVisible
    color: "transparent"

    anchors {
        left: true
        right: true
        top: true
        bottom: true
    }

    focusable: true

    WlrLayershell.layer: WlrLayer.Overlay
    WlrLayershell.exclusiveZone: 0
    WlrLayershell.keyboardFocus: WlrKeyboardFocus.OnDemand

    property var parentTheme: null
    property var theme: parentTheme || themeLoader.item
    property real lastBackupTime: 0

    Loader {
        id: themeLoader
        source: "../theme.qml"
    }

    Timer {
        id: shuffleDelayTimer
        repeat: false
        onTriggered: performShuffleSequence(0)
    }

    Timer {
        id: shuffleStepTimer
        repeat: false
        property int currentShuffleCount: 0
        property var currentBottomCard: null

        onTriggered: {
            if (currentBottomCard) {
                currentBottomCard.shuffleX = cardContainer.width / 2 - 100;
                currentBottomCard.shuffleY = 0;
                currentBottomCard.shuffleZ = -1;

                for (var j = 0; j < cardRepeater.count; j++) {
                    var otherCard = cardRepeater.itemAt(j);
                    if (otherCard && otherCard !== currentBottomCard) {
                        otherCard.shuffleZ += 1;
                    }
                }

                shuffleNextTimer.currentShuffleCount = currentShuffleCount + 1;
                shuffleNextTimer.interval = theme.animationDuration;
                shuffleNextTimer.start();
            }
        }
    }

    Timer {
        id: shuffleNextTimer
        repeat: false
        property int currentShuffleCount: 0
        onTriggered: performShuffleSequence(currentShuffleCount)
    }

    Timer {
        id: dealTimer
        repeat: false
        property int dealIndex: 0

        onTriggered: {
            var targetCard = cardRepeater.itemAt(dealIndex);
            if (targetCard) {
                var swipeDirection = dealIndex === 0 ? -150 : dealIndex === 2 ? 150 : 0;
                targetCard.shuffleX = targetCard.originalX + swipeDirection;
                targetCard.shuffleY = -40;
                targetCard.shuffleZ = dealIndex;

                // Then settle to final position
                settleTimer.targetCard = targetCard;
                settleTimer.interval = theme.animationDuration;
                settleTimer.start();
            }

            // Deal next card
            dealIndex++;
            if (dealIndex < cardRepeater.count) {
                interval = 200;
                start();
            } else {
                // All cards dealt, reset shuffling state
                finishTimer.interval = theme.slowAnimationDuration * 2;
                finishTimer.start();
            }
        }
    }

    Timer {
        id: settleTimer
        repeat: false
        property var targetCard: null

        onTriggered: {
            if (targetCard) {
                targetCard.shuffleX = targetCard.originalX;
                targetCard.shuffleY = targetCard.originalY;
                targetCard.shuffleZ = 0;
            }
        }
    }

    Timer {
        id: finishTimer
        repeat: false
        onTriggered: {
            cardContainer.isShuffling = false;
        }
    }

    Component.onCompleted: {
        if (typeof WallpaperModules !== "undefined" && WallpaperModules.WallpaperService) {
            var serviceLoaded = WallpaperModules.WallpaperService.isLoaded;
        }
    }

    function showOverlay() {
        if (WallpaperModules.WallpaperService)
            WallpaperModules.WallpaperService.refreshVisibleWallpapers();
        overlayVisible = true;
    }

    function hideOverlay() {
        overlayVisible = false;
        overlayHidden();
    }

    onOverlayVisibleChanged: {
        if (overlayVisible) {
            // Refresh wallpapers when overlay becomes visible
            if (WallpaperModules.WallpaperService) {
                WallpaperModules.WallpaperService.refreshVisibleWallpapers();
            }
        }
    }

    function getCenterPosition() {
        var totalWidth = (3 * 200) + (2 * 50);
        return {
            x: (wallpaperCarousel.width - totalWidth) / 2,
            y: (wallpaperCarousel.height - 300) / 2
        };
    }

    function resetCardStates() {
        for (var i = 0; i < cardRepeater.count; i++) {
            var card = cardRepeater.itemAt(i);
            if (card) {
                card.isFlipped = false;
                card.isHovered = false;
                // Reset card scale to normal size
                card.targetScale = 1.0;
                card.cardScale = 1.0;
            }
        }
    }

    function startShuffleAnimation() {
        cardContainer.isShuffling = true;
        resetCardStates();

        // Phase 1: Move all cards to center stack position
        for (var i = 0; i < cardRepeater.count; i++) {
            var card = cardRepeater.itemAt(i);
            if (card) {
                card.shuffleX = cardContainer.width / 2 - 100; // Center position
                card.shuffleY = 0;
                card.shuffleZ = i; // Initial stack order
            }
        }

        // Phase 2: Start the shuffle sequence after cards reach center
        shuffleDelayTimer.interval = theme.slowAnimationDuration + 100;
        shuffleDelayTimer.start();
    }

    function performShuffleSequence(shuffleCount) {
        if (shuffleCount >= 3) {
            // Shuffling complete, now deal the cards
            dealCardsOut();
            return;
        }

        // Take bottom card (highest z-index) and move it to top
        var bottomCard = null;
        var maxZ = -1;

        // Find the card with highest z-index (bottom of stack)
        for (var i = 0; i < cardRepeater.count; i++) {
            var card = cardRepeater.itemAt(i);
            if (card && card.shuffleZ > maxZ) {
                maxZ = card.shuffleZ;
                bottomCard = card;
            }
        }

        if (bottomCard) {
            // Animate bottom card up and to the side, then back to top of stack
            bottomCard.shuffleX = cardContainer.width / 2 - 50; // Slightly to the side
            bottomCard.shuffleY = -30; // Lift up

            // Set up the timer to move card to top of stack
            shuffleStepTimer.currentBottomCard = bottomCard;
            shuffleStepTimer.currentShuffleCount = shuffleCount;
            shuffleStepTimer.interval = theme.animationDuration;
            shuffleStepTimer.start();
        }
    }

    function dealCardsOut() {
        // Refresh wallpapers for new random selection
        if (WallpaperModules.WallpaperService)
            WallpaperModules.WallpaperService.refreshVisibleWallpapers();

        // Start dealing cards using the dealTimer
        dealTimer.dealIndex = 0;
        dealTimer.interval = 200;
        dealTimer.start();
    }

    function triggerBackup() {
        var currentTime = Date.now();
        if (currentTime - lastBackupTime < 1000) {
            return;
        }

        lastBackupTime = currentTime;
        var backupComponent = Qt.createComponent("wallpaperBackup.qml");
        if (backupComponent.status === Component.Ready) {
            var backupObj = backupComponent.createObject(wallpaperCarousel);
        }
    }

    Rectangle {
        id: backgroundOverlay
        anchors.fill: parent
        color: "transparent"

        MouseArea {
            id: overlayClickCatcher
            anchors.fill: parent
            propagateComposedEvents: true
            acceptedButtons: Qt.LeftButton | Qt.RightButton

            onClicked: function (mouse) {
                if (!overlayVisible)
                    return;

                var clickX = mouse.x;
                var clickY = mouse.y;

                // Calculate content boundaries (cards + shuffle button area)
                var contentLeft = cardContainer.x;
                var contentTop = cardContainer.y;
                var contentRight = cardContainer.x + cardContainer.width;
                var contentBottom = shuffleButton.y + shuffleButton.height;

                // Check if click is outside the content area
                if (clickX < contentLeft || clickX > contentRight || clickY < contentTop || clickY > contentBottom) {

                    // Outside content: close overlay first, then trigger backup
                    hideOverlay();
                    Qt.callLater(triggerBackup);
                    mouse.accepted = true;
                } else {
                    // Inside content area: let child elements handle it
                    mouse.accepted = false;
                }
            }
        }
    }

    Item {
        id: cardContainer

        property var centerPos: getCenterPosition()
        x: centerPos.x
        y: centerPos.y
        width: (3 * 200) + (2 * 50) // Total width for 3 cards with spacing
        height: 300

        property bool isShuffling: false

        Repeater {
            id: cardRepeater
            model: 3

            WallpaperCard {
                id: card

                property var wallpaperData: {
                    if (typeof WallpaperModules !== "undefined" && WallpaperModules.WallpaperService) {
                        var visibleWallpapers = WallpaperModules.WallpaperService.getVisibleWallpapers();
                        return index < visibleWallpapers.length ? visibleWallpapers[index] : null;
                    }
                    return null;
                }

                wallpaperPath: wallpaperData ? wallpaperData.path : ""
                wallpaperName: wallpaperData ? wallpaperData.displayName : ""

                // Animation properties
                property real originalX: index * 250 // 200px card + 50px spacing
                property real originalY: 0
                property real shuffleX: originalX
                property real shuffleY: originalY
                property int shuffleZ: 0

                x: shuffleX
                y: shuffleY
                z: shuffleZ

                Behavior on shuffleX {
                    NumberAnimation {
                        duration: theme.slowAnimationDuration
                        easing.type: Easing.OutCubic
                    }
                }

                Behavior on shuffleY {
                    NumberAnimation {
                        duration: theme.slowAnimationDuration
                        easing.type: Easing.OutCubic
                    }
                }

                Behavior on shuffleZ {
                    NumberAnimation {
                        duration: theme.animationDuration
                        easing.type: Easing.InOutQuad
                    }
                }
            }
        }
    }

    Rectangle {
        id: shuffleButton
        width: 80
        height: 40
        radius: 20
        color: theme.backgroundSecondary
        border.width: 1
        border.color: theme.border

        anchors.horizontalCenter: cardContainer.horizontalCenter
        anchors.top: cardContainer.bottom
        anchors.topMargin: 40

        Text {
            anchors.centerIn: parent
            text: ""
            color: theme.textPrimary
            font.pixelSize: 12
            font.weight: Font.Bold
        }

        MouseArea {
            anchors.fill: parent
            onClicked: {
                if (!cardContainer.isShuffling) {
                    startShuffleAnimation();
                }
            }
        }
    }

    Connections {
        target: WallpaperModules.WallpaperService
        function onCarouselStartIndexChanged() {
            // Refresh all cards when wallpapers change
            for (var i = 0; i < cardRepeater.count; i++) {
                var card = cardRepeater.itemAt(i);
                if (card && card.updateWallpaperData) {
                    card.updateWallpaperData();
                }
            }
        }
    }
}
