import QtQuick
import Quickshell.Io

Process {
    id: backupProcess
    
    property string backupDir: "/home/<USER>/Documents/quickshell_backups"
    property string timestamp: new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5)
    
    command: ["sh", "-c", createBackupCommand()]
    running: true
    
    function createBackupCommand() {
        var cmd = "mkdir -p " + backupDir + " && "
        cmd += "BACKUP_NAME='backup_" + timestamp + "' && "
        cmd += "mkdir -p " + backupDir + "/$BACKUP_NAME && "
        cmd += "cp -r /home/<USER>/.config/quickshell/bar/modules/wallpaperModules " + backupDir + "/$BACKUP_NAME/ && "
        cmd += "cp -r /home/<USER>/.git/dots/quickshell " + backupDir + "/$BACKUP_NAME/ && "
        cmd += "echo 'Backup created: " + backupDir + "/$BACKUP_NAME'"
        return cmd
    }
    
    onExited: function(exitCode, exitStatus) {
        if (exitCode === 0) {
            console.log("Wallpaper backup completed successfully")
        } else {
            console.log("Wallpaper backup failed with exit code:", exitCode)
        }
        destroy()
    }
}