import QtQuick
import Qt5Compat.GraphicalEffects
import "../"
import "." as WallpaperModules

Rectangle {
    id: wallpaperCard

    property string wallpaperPath: ""
    property string wallpaperName: ""
    property bool isFlipped: false
    property bool isHovered: false

    property var parentTheme: null
    property var theme: parentTheme || themeLoader.item

    Loader {
        id: themeLoader
        source: "../theme.qml"
    }

    width: 200
    height: 300
    radius: theme.largeBorderRadius
    color: "transparent"
    
    property real cardScale: 1.0
    property real targetScale: 1.0



    MouseArea {
        id: cardMouseArea
        anchors.fill: parent
        hoverEnabled: true

        onEntered: {
            if (parent.parent && parent.parent.isShuffling) {
                return
            }
            
            isHovered = true
            targetScale = 1.1
            scaleAnimation.start()
            Qt.callLater(function() {
                flipAnimation.to = 180
                flipAnimation.start()
            })
        }

        onExited: {
            isHovered = false
            targetScale = 1.0
            scaleAnimation.start()
            flipTimer.start()
        }

        onClicked: {
            if (isFlipped && wallpaperPath !== "") {
                if (WallpaperModules.WallpaperService) WallpaperModules.WallpaperService.applyWallpaper(wallpaperPath)
                var carousel = wallpaperCard
                while (carousel && !carousel.hasOwnProperty("hideOverlay")) {
                    carousel = carousel.parent
                }
                if (carousel && carousel.hideOverlay) {
                    carousel.hideOverlay()
                }
            }
        }
    }

    Timer {
        id: flipTimer
        interval: 500
        onTriggered: {
            if (!isHovered) {
                flipAnimation.to = 0
                flipAnimation.start()
            }
        }
    }

    transform: [
        Rotation {
            id: cardRotation
            axis.x: 0
            axis.y: 1
            axis.z: 0
            angle: 0
            origin.x: wallpaperCard.width / 2
            origin.y: wallpaperCard.height / 2
        },
        Scale {
            id: cardScale
            xScale: wallpaperCard.cardScale
            yScale: wallpaperCard.cardScale
            origin.x: wallpaperCard.width / 2
            origin.y: wallpaperCard.height / 2
        }
    ]

    NumberAnimation {
        id: flipAnimation
        target: cardRotation
        property: "angle"
        duration: theme.animationDuration
        easing.type: Easing.InOutQuad

        onFinished: {
            isFlipped = cardRotation.angle > 90
        }
    }

    NumberAnimation {
        id: scaleAnimation
        target: wallpaperCard
        property: "cardScale"
        to: targetScale
        duration: theme.fastAnimationDuration
        easing.type: Easing.OutCubic
    }

    Rectangle {
        id: cardBack
        anchors.fill: parent
        radius: theme.largeBorderRadius
        color: theme.backgroundSecondary
        visible: cardRotation.angle <= 90

        Grid {
            id: patternGrid
            anchors.fill: parent
            anchors.margins: theme.largeBorderRadius
            clip: true

            property int patternSize: 50
            property int tilesX: Math.ceil(parent.width / patternSize)
            property int tilesY: Math.ceil(parent.height / patternSize)

            columns: tilesX
            rows: tilesY

            Repeater {
                model: patternGrid.tilesX * patternGrid.tilesY

                Image {
                    width: patternGrid.patternSize
                    height: patternGrid.patternSize
                    source: "file:///home/<USER>/.config/quickshell/bar/icons/pattern-sign.png"
                    fillMode: Image.PreserveAspectFit
                    smooth: true
                    cache: true
                    opacity: 0.35

                    onStatusChanged: {
                        if (status === Image.Error) {
                            visible = false
                        }
                    }
                }
            }
        }
    }

    Rectangle {
        id: cardFront
        anchors.fill: parent
        radius: theme.largeBorderRadius
        color: theme.backgroundSecondary
        visible: cardRotation.angle > 90

        Grid {
            id: frontPatternGrid
            anchors.fill: parent
            anchors.margins: theme.largeBorderRadius
            clip: true

            property int patternSize: 50
            property int tilesX: Math.ceil(parent.width / patternSize)
            property int tilesY: Math.ceil(parent.height / patternSize)

            columns: tilesX
            rows: tilesY

            Repeater {
                model: frontPatternGrid.tilesX * frontPatternGrid.tilesY

                Image {
                    width: frontPatternGrid.patternSize
                    height: frontPatternGrid.patternSize
                    source: "file:///home/<USER>/.config/quickshell/bar/icons/pattern-sign.png"
                    fillMode: Image.PreserveAspectFit
                    smooth: true
                    cache: true
                    opacity: 0.2

                    onStatusChanged: {
                        if (status === Image.Error) {
                            visible = false
                        }
                    }
                }
            }
        }

        Rectangle {
            id: imageContainer
            anchors.fill: parent
            anchors.margins: 20
            radius: theme.borderRadius
            color: "transparent"
            clip: true

            Image {
                id: wallpaperImage
                anchors.fill: parent
                source: wallpaperPath !== "" ? "file://" + wallpaperPath : ""
                fillMode: Image.PreserveAspectCrop
                smooth: true
                cache: true

                onStatusChanged: {
                    if (status === Image.Error) {
                        visible = false
                    }
                }
            }

            Rectangle {
                id: textBackground
                anchors.right: wallpaperImage.right
                anchors.bottom: wallpaperImage.bottom
                anchors.rightMargin: 8
                anchors.bottomMargin: 8
                width: Math.min(nameText.implicitWidth + 12, wallpaperImage.width - 16)
                height: nameText.implicitHeight + 8
                radius: theme.smallBorderRadius
                color: Qt.rgba(0, 0, 0, 0.8)
                z: 10

                transform: Scale {
                    xScale: -1
                    origin.x: textBackground.width / 2
                }

                Text {
                    id: nameText
                    anchors.centerIn: parent
                    text: wallpaperName
                    color: "white"
                    font.pixelSize: 10
                    font.weight: Font.Bold
                    font.family: "Deutsch Gothic"
                    elide: Text.ElideRight
                    maximumLineCount: 1
                    wrapMode: Text.NoWrap
                }
            }
        }
    }
}