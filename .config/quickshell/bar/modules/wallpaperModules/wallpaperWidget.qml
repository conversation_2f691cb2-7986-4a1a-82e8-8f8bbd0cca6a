import QtQuick
import QtQuick.Layouts
import Qt5Compat.GraphicalEffects
import Quickshell.Io
import "../"

Rectangle {
    id: wallpaperWidget
    
    width: 32
    height: 18
    radius: 9
    color: "transparent"
    
    property var wallpaperCarousel: null
    property var parentIpcManager: null

    property var parentTheme: null
    property var theme: parentTheme
    
    Image {
        anchors.centerIn: parent
        width: 14
        height: 12
        source: "file:///home/<USER>/.config/quickshell/bar/icons/playing-cards-icon.svg"
        fillMode: Image.PreserveAspectFit
        smooth: true
        
        ColorOverlay {
            anchors.fill: parent
            source: parent
            color: theme ? theme.brightMagenta : "#ee5396"
        }
    }
    
    MouseArea {
        anchors.fill: parent
        hoverEnabled: true
        
        onClicked: {
            // Use IPC to toggle wallpaper overlay
            var process = Qt.createQmlObject('import Quickshell.Io; Process {}', wallpaperWidget);
            process.exec(["qs", "ipc", "call", "wallpaper", "toggle"]);
        }
    }
    

}