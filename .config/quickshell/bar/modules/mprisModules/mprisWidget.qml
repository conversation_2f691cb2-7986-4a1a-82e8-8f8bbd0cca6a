import QtQuick
import QtQuick.Layouts
import Quickshell.Services.Mpris
import Quickshell.Io
import "../"
import "."

Rectangle {
    id: mprisWidget
    objectName: "mprisWidget"
    width: visible ? (mprisContent.width + 16) : 0
    height: 18
    radius: 9
    color: mouseArea.containsMouse ? (theme ? Qt.rgba(theme.accent.r, theme.accent.g, theme.accent.b, 0.2) : Qt.rgba(0.235, 0.859, 0.851, 0.2)) : "transparent"


    property int maxTrackInfoWidth: 200
    property bool showWhenNoPlayer: true


    visible: activePlayer !== null || showWhenNoPlayer


    signal showMprisOverlayRequested()
    signal hideMprisOverlayRequested()
    signal playerStateChanged(var player)
    signal trackChanged(string title, string artist)


    readonly property var activePlayer: Mpris.players.values.length > 0 ? Mpris.players.values[0] : null


    property var previousPlayer: null
    

    onActivePlayerChanged: {
        if (activePlayer !== previousPlayer) {
            previousPlayer = activePlayer
            playerStateChanged(activePlayer)
        }
    }


    property var parentTheme: null
    property var theme: parentTheme
    property var debuggerInstance: null


    RowLayout {
        id: mprisContent
        anchors.centerIn: parent
        spacing: 8


        Text {
            id: playIcon
            text: {
                if (!activePlayer) return "󰝚"
                switch (activePlayer.playbackState) {
                    case MprisPlaybackState.Playing: return "󰏤"
                    case MprisPlaybackState.Paused: return "󰐊"
                    default: return "󰓛"
                }
            }
            color: theme ? theme.textPrimary : "#f2f4f8"
            font.pixelSize: 14
            font.family: "JetBrains Mono, monospace"
        }


        Text {
            id: trackInfo
            text: {
                if (!activePlayer) return "No media"
                
                var title = activePlayer.trackTitle || "Unknown"
                var artist = activePlayer.trackArtists && activePlayer.trackArtists.length > 0
                    ? activePlayer.trackArtists[0] : ""

                return artist ? title + " • " + artist : title
            }
            color: theme ? theme.textSecondary : "#999999"
            font.pixelSize: 11
            font.family: "JetBrains Mono, monospace"

            Layout.maximumWidth: maxTrackInfoWidth
            elide: Text.ElideRight
        }
    }


    MouseArea {
        id: mouseArea
        anchors.fill: parent
        acceptedButtons: Qt.LeftButton | Qt.RightButton
        hoverEnabled: true

        onClicked: function(mouse) {
            if (mouse.button === Qt.LeftButton) {
                // Use IPC to toggle MPRIS overlay
                var process = Qt.createQmlObject('import Quickshell.Io; Process {}', mprisWidget);
                process.exec(["qs", "ipc", "call", "mpris", "toggle"]);
            } else if (mouse.button === Qt.RightButton && activePlayer) {
                togglePlayback()
            }
        }

        onEntered: {
        }

        onExited: {
        }
    }


    Behavior on color {
        ColorAnimation { 
            duration: theme ? theme.animationDuration : 200
            easing.type: Easing.OutCubic
        }
    }

    Behavior on width {
        NumberAnimation { 
            duration: theme ? theme.slowAnimationDuration : 400
            easing.type: Easing.OutCubic 
        }
    }


    function togglePlayback() {
        if (!activePlayer) return
        
        if (debuggerInstance) debuggerInstance.logMpris("togglePlayback called, using togglePlaying()")
        activePlayer.togglePlaying()
    }

    function playNext() {
        if (activePlayer && activePlayer.canGoNext) {
            activePlayer.next()
        }
    }

    function playPrevious() {
        if (activePlayer && activePlayer.canGoPrevious) {
            activePlayer.previous()
        }
    }

    function getCurrentTrackInfo() {
        if (!activePlayer) return { title: "", artist: "", album: "" }
        
        return {
            title: activePlayer.trackTitle || "",
            artist: activePlayer.trackArtists && activePlayer.trackArtists.length > 0 
                ? activePlayer.trackArtists[0] : "",
            album: activePlayer.trackAlbum || ""
        }
    }
}