pragma Singleton
import QtQuick
import Quickshell
import Quickshell.Services.Mpris

Singleton {
    id: root

    readonly property list<MprisPlayer> list: Mpris.players.values
    readonly property MprisPlayer active: manualActive ?? list.find(p => p.identity === "Spotify") ?? list[0] ?? null
    property MprisPlayer manualActive


    function togglePlaying() {
        if (active && active.canTogglePlaying) {
            active.togglePlaying()
        }
    }

    function previous() {
        if (active && active.canGoPrevious) {
            active.previous()
        }
    }

    function next() {
        if (active && active.canGoNext) {
            active.next()
        }
    }

    function stop() {
        if (active) {
            active.stop()
        }
    }

    function play() {
        if (active && active.canPlay) {
            active.play()
        }
    }

    function pause() {
        if (active && active.canPause) {
            active.pause()
        }
    }
}