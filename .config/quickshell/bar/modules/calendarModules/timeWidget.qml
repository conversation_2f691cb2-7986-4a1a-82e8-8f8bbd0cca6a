import QtQuick
import "../"

Rectangle {
    id: timeWidget
    objectName: "timeWidget"
    width: timeText.width + 20
    height: 18
    radius: 9
    color: mouseArea.containsMouse ? (theme ? Qt.rgba(theme.accent.r, theme.accent.g, theme.accent.b, 0.2) : Qt.rgba(0.235, 0.859, 0.851, 0.2)) : "transparent"


    property date currentDate: new Date()
    property string timeFormat: "h:mm AP"


    signal showCalendarOverlayRequested()
    signal hideCalendarOverlayRequested()
    signal timeClicked()


    property var parentTheme: null
    property var theme: parentTheme


    Text {
        id: timeText
        anchors.centerIn: parent
        text: Qt.formatDateTime(currentDate, timeFormat)
        color: theme ? theme.textPrimary : "#f2f4f8"
        font.pixelSize: 12
        font.family: "JetBrains Mono, monospace"
        font.weight: Font.Medium
    }


    MouseArea {
        id: mouseArea
        anchors.fill: parent
        hoverEnabled: true
        acceptedButtons: Qt.LeftButton | Qt.RightButton

        onClicked: function(mouse) {
            if (mouse.button === Qt.LeftButton) {
                timeClicked()
                showCalendarOverlayRequested()
            }
        }

        onEntered: {
        }

        onExited: {
        }
    }


    Behavior on color {
        ColorAnimation { 
            duration: theme ? theme.animationDuration : 200
            easing.type: Easing.OutCubic
        }
    }


    Timer {
        id: updateTimer
        interval: 1000
        running: true
        repeat: true
        onTriggered: {
            currentDate = new Date()
        }
    }


    function updateTime() {
        currentDate = new Date()
    }

    function setTimeFormat(format) {
        timeFormat = format
    }
}