#!/usr/bin/env python3

import os
import sys
import argparse
from datetime import datetime
from pathlib import Path

def createCalendarDirectoryStructure(basePath, year, month):
    yearDir = basePath / str(year)
    monthDir = yearDir / f"{month:02d}"
    
    monthDir.mkdir(parents=True, exist_ok=True)
    return monthDir

def generateMarkdownTemplate(dateStr):
    template = f"""# {dateStr}

## #event

## #reminder

## #bananas
"""
    return template

def createOrOpenDateFile(dateStr):
    try:
        dateObj = datetime.strptime(dateStr, "%Y-%m-%d")
        
        homeDir = Path.home()
        calendarNotesDir = homeDir / "Documents" / "todo"
        
        monthDir = createCalendarDirectoryStructure(
            calendarNotesDir, 
            dateObj.year, 
            dateObj.month
        )
        
        filePath = monthDir / f"{dateStr}.md"
        
        if not filePath.exists():
            template = generateMarkdownTemplate(dateStr)
            with open(filePath, 'w', encoding='utf-8') as f:
                f.write(template)
            print(f"Created new calendar file: {filePath}")
        else:
            print(f"Calendar file already exists: {filePath}")
        
        try:
            import subprocess
            subprocess.run([
                "hyprctl", "dispatch", "exec", 
                f"[float;noanim] ghostty -e nvim '{filePath}'"
            ], check=False)
        except Exception as e:
            print(f"Could not open file automatically: {e}")
            print(f"File location: {filePath}")
        
        return str(filePath)
        
    except ValueError as e:
        print(f"Invalid date format. Expected YYYY-MM-DD, got: {dateStr}")
        return None
    except Exception as e:
        print(f"Error creating calendar file: {e}")
        return None

def listCalendarFiles(year=None, month=None):
    homeDir = Path.home()
    calendarNotesDir = homeDir / "Documents" / "todo"
    
    if not calendarNotesDir.exists():
        print("No calendar notes directory found.")
        return
    
    if year and month:
        searchDir = calendarNotesDir / str(year) / f"{month:02d}"
        if searchDir.exists():
            files = list(searchDir.glob("*.md"))
            print(f"Calendar files for {year}-{month:02d}:")
            for file in sorted(files):
                print(f"  {file.name}")
        else:
            print(f"No files found for {year}-{month:02d}")
    elif year:
        yearDir = calendarNotesDir / str(year)
        if yearDir.exists():
            print(f"Calendar files for {year}:")
            for monthDir in sorted(yearDir.iterdir()):
                if monthDir.is_dir():
                    files = list(monthDir.glob("*.md"))
                    if files:
                        print(f"  {monthDir.name}:")
                        for file in sorted(files):
                            print(f"    {file.name}")
        else:
            print(f"No files found for {year}")
    else:
        print("All calendar files:")
        for yearDir in sorted(calendarNotesDir.iterdir()):
            if yearDir.is_dir():
                print(f"  {yearDir.name}:")
                for monthDir in sorted(yearDir.iterdir()):
                    if monthDir.is_dir():
                        files = list(monthDir.glob("*.md"))
                        if files:
                            print(f"    {monthDir.name}: {len(files)} files")

def main():
    parser = argparse.ArgumentParser(description="Calendar markdown file manager")
    parser.add_argument("date", nargs="?", help="Date in YYYY-MM-DD format")
    parser.add_argument("--list", action="store_true", help="List existing calendar files")
    parser.add_argument("--year", type=int, help="Year for listing files")
    parser.add_argument("--month", type=int, help="Month for listing files")
    
    args = parser.parse_args()
    
    if args.list:
        listCalendarFiles(args.year, args.month)
    elif args.date:
        result = createOrOpenDateFile(args.date)
        if result:
            sys.exit(0)
        else:
            sys.exit(1)
    else:
        parser.print_help()
        sys.exit(1)

if __name__ == "__main__":
    main()