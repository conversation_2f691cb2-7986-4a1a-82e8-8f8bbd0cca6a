import QtQuick
import QtQuick.Layouts
import Quickshell
import Quickshell.Wayland
import Quickshell.Hyprland
import Quickshell.Services.SystemTray
import Quickshell.Io
import "./modules/wallpaperModules" as WallpaperModules

PanelWindow {
    id: window

    property var targetScreen: Quickshell.screens[0]
    property int screenIndex: 0

    property var timeModuleItem: null
    property var notificationModuleItem: null

    screen: targetScreen

    property var theme: themeLoader.item

    Loader {
        id: themeLoader
        source: "./modules/theme.qml"
    }
    property var debuggerInstance: debuggerLoader.item
    property var ipcPanelManager: ipcManagerServiceLoader.item ? ipcManagerServiceLoader.item.ipcManager : null

    Loader {
        id: debuggerLoader
        source: "./modules/debugger.qml"
    }

    // Service loaders for testing
    Loader {
        id: mprisServiceLoader
        source: "./modules/services/mprisService.qml"
        onLoaded: {
            if (item) {
                item.parentTheme = theme;
                item.targetScreen = targetScreen;
                item.debuggerInstance = debuggerInstance;
                item.ipcPanelManager = ipcPanelManager;
                console.log("mprisService loaded successfully");
            }
        }
        onStatusChanged: {
            if (status === Loader.Error) {
                console.error("Failed to load mprisService:", sourceComponent.errorString());
            }
        }
    }

    Loader {
        id: notificationServiceLoader
        source: "./modules/services/notificationService.qml"
        onLoaded: {
            if (item) {
                item.parentTheme = theme;
                item.targetScreen = targetScreen;
                item.debuggerInstance = debuggerInstance;
                item.ipcPanelManager = ipcPanelManager;
                console.log("notificationService loaded successfully");
            }
        }
        onStatusChanged: {
            if (status === Loader.Error) {
                console.error("Failed to load notificationService:", sourceComponent.errorString());
            }
        }
    }

    Loader {
        id: calendarServiceLoader
        source: "./modules/services/calendarService.qml"
        onLoaded: {
            if (item) {
                item.parentTheme = theme;
                item.targetScreen = targetScreen;
                item.debuggerInstance = debuggerInstance;
                item.ipcPanelManager = ipcPanelManager;
                console.log("calendarService loaded successfully");

                // If time widget is already loaded, set it up now
                if (window.timeModuleItem) {
                    console.log("bar.qml: Setting up time widget after calendarService load");
                    item.setupWidget(window.timeModuleItem);
                }
            }
        }
        onStatusChanged: {
            if (status === Loader.Error) {
                console.error("Failed to load calendarService:", sourceComponent.errorString());
            }
        }
    }

    Loader {
        id: wallpaperServiceLoader
        source: "./modules/services/wallpaperService.qml"
        onLoaded: {
            if (item) {
                item.parentTheme = theme;
                item.targetScreen = targetScreen;
                item.debuggerInstance = debuggerInstance;
                item.ipcPanelManager = ipcPanelManager;
                console.log("wallpaperService loaded successfully");
            }
        }
        onStatusChanged: {
            if (status === Loader.Error) {
                console.error("Failed to load wallpaperService:", sourceComponent.errorString());
            }
        }
    }

    Loader {
        id: ipcManagerServiceLoader
        source: "./modules/services/ipcManagerService.qml"
        onLoaded: {
            if (item) {
                item.parentTheme = theme;
                item.targetScreen = targetScreen;
                item.debuggerInstance = debuggerInstance;
                console.log("ipcManagerService loaded successfully");
            }
        }
        onStatusChanged: {
            if (status === Loader.Error) {
                console.error("Failed to load ipcManagerService:", sourceComponent.errorString());
            }
        }
    }

    property var workspaceRanges: [[1, 9]]

    property var screenConfigs: [
        {
            left: ["workspaces", "windowTitle"],
            center: ["time", "mpris"],
            right: ["wallpaper", "network", "volume", "system", "notification"]
        }
    ]

    property var availableModules: {
        "workspaces": "hyprlandWorkspaces",
        "windowTitle": "windowTitleWidget",
        "time": "calendarModules/timeWidget",
        "system": "systemIndicators",
        "mpris": "mprisModules/mprisWidget",
        "volume": "volumeWidget",
        "network": "networkWidget",
        "notification": "notificationModules/notificationWidget",
        "wallpaper": "wallpaperModules/wallpaperWidget"
    }

    property var currentConfig: getScreenConfig(screenIndex)

    anchors {
        left: true
        right: true
        top: true
        bottom: false
    }

    implicitHeight: 30
    visible: true
    color: "transparent"

    WlrLayershell.layer: WlrLayer.Top
    WlrLayershell.exclusiveZone: 30
    WlrLayershell.keyboardFocus: WlrKeyboardFocus.None
    WlrLayershell.namespace: "quickshell-bar"

    Component.onCompleted: {
        if (debuggerInstance)
            debuggerInstance.logBar("Static brackets bar initialized for screen " + screenIndex);
        if (ipcPanelManager)
            ipcPanelManager.logIpc("Bar window initialized, setting up IPC integration");
        // All service registration is now handled by the services themselves
    }

    Rectangle {
        id: bar
        anchors.fill: parent
        radius: 0
        color: theme ? theme.background : "#000000"

        RowLayout {
            anchors.fill: parent
            anchors.leftMargin: 2
            anchors.rightMargin: 2
            anchors.topMargin: 5
            anchors.bottomMargin: 6
            spacing: 4

            Row {
                Layout.alignment: Qt.AlignLeft
                spacing: 10

                Repeater {
                    model: currentConfig.left

                    Loader {
                        source: getModuleSource(modelData)
                        onLoaded: {
                            if (modelData === "workspaces" && item) {
                                item.screenIndex = window.screenIndex;
                            }
                            // Pass theme to items that declare parentTheme property
                            if (item && theme && item.hasOwnProperty("parentTheme")) {
                                item.parentTheme = theme;
                            }
                        }
                    }
                }
            }

            Item {
                Layout.fillWidth: true
                Layout.fillHeight: true
                Layout.alignment: Qt.AlignHCenter

                Row {
                    anchors.centerIn: parent
                    spacing: 0

                    Text {
                        text: "【"
                        color: theme ? theme.accent : "#3ddbd9"
                        font.pixelSize: 14
                        font.family: "JetBrains Mono, monospace"
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    Row {
                        id: centerContent
                        spacing: 10
                        anchors.verticalCenter: parent.verticalCenter

                        Repeater {
                            model: currentConfig.center

                            Loader {
                                source: getModuleSource(modelData)
                                onLoaded: {
                                    if (modelData === "workspaces" && item) {
                                        item.screenIndex = window.screenIndex;
                                    }
                                    // Pass theme to items that declare parentTheme property
                                    if (item && theme && item.hasOwnProperty("parentTheme")) {
                                        item.parentTheme = theme;
                                    }
                                    // Special handling for time widget - register with calendarService
                                    if (modelData === "time" && item) {
                                        window.timeModuleItem = item;
                                        console.log("bar.qml: Time widget loaded, calendarServiceLoader.item:", !!calendarServiceLoader.item);
                                        // Register this widget with the calendarService
                                        if (calendarServiceLoader.item) {
                                            calendarServiceLoader.item.setupWidget(item);
                                        } else {
                                            console.log("bar.qml: calendarServiceLoader.item not available yet, will retry");
                                            // Retry when calendarService becomes available
                                            Qt.callLater(function () {
                                                if (calendarServiceLoader.item && item) {
                                                    console.log("bar.qml: Retrying setupWidget for time widget");
                                                    calendarServiceLoader.item.setupWidget(item);
                                                }
                                            });
                                        }
                                    }
                                }
                            }
                        }
                    }

                    Text {
                        text: "】"
                        color: theme ? theme.accent : "#3ddbd9"
                        font.pixelSize: 14
                        font.family: "JetBrains Mono, monospace"
                        anchors.verticalCenter: parent.verticalCenter
                    }
                }
            }

            Row {
                Layout.alignment: Qt.AlignRight
                spacing: 0
                visible: window.screenIndex === 0

                Text {
                    text: "【"
                    color: theme ? theme.accent : "#3ddbd9"
                    font.pixelSize: 14
                    font.family: "JetBrains Mono, monospace"
                    anchors.verticalCenter: parent.verticalCenter
                }

                Loader {
                    id: visualizer
                    source: "./modules/visualizerModules/visualizerWidget.qml"
                    anchors.verticalCenter: parent.verticalCenter
                    onLoaded: {
                        if (item && theme && item.hasOwnProperty("parentTheme")) {
                            item.parentTheme = theme;
                        }
                    }
                }

                Text {
                    text: "】"
                    color: theme ? theme.accent : "#3ddbd9"
                    font.pixelSize: 14
                    font.family: "JetBrains Mono, monospace"
                    anchors.verticalCenter: parent.verticalCenter
                }
            }

            Row {
                Layout.alignment: Qt.AlignRight
                spacing: 0
                visible: SystemTray.items && SystemTray.items.values && SystemTray.items.values.length > 0

                Text {
                    text: "【"
                    color: theme ? theme.accent : "#3ddbd9"
                    font.pixelSize: 14
                    font.family: "JetBrains Mono, monospace"
                    anchors.verticalCenter: parent.verticalCenter
                }

                Loader {
                    id: systemTray
                    source: "./modules/systemTrayWidget.qml"
                    anchors.verticalCenter: parent.verticalCenter
                    onLoaded: {
                        if (item && theme && item.hasOwnProperty("parentTheme")) {
                            item.parentTheme = theme;
                        }
                    }
                }

                Text {
                    text: "】"
                    color: theme ? theme.accent : "#3ddbd9"
                    font.pixelSize: 14
                    font.family: "JetBrains Mono, monospace"
                    anchors.verticalCenter: parent.verticalCenter
                }
            }

            Row {
                Layout.alignment: Qt.AlignRight
                spacing: 0

                Text {
                    text: "【"
                    color: theme ? theme.accent : "#3ddbd9"
                    font.pixelSize: 14
                    font.family: "JetBrains Mono, monospace"
                    anchors.verticalCenter: parent.verticalCenter
                }

                Row {
                    id: rightContent
                    spacing: 0
                    anchors.verticalCenter: parent.verticalCenter

                    Repeater {
                        model: currentConfig.right

                        Loader {
                            source: getModuleSource(modelData)
                            onLoaded: {
                                if (modelData === "workspaces" && item) {
                                    item.screenIndex = window.screenIndex;
                                }
                                // Pass theme to items that declare parentTheme property
                                if (item && theme && item.hasOwnProperty("parentTheme")) {
                                    item.parentTheme = theme;
                                }
                                if (modelData === "notification" && item) {
                                    // notification widget now managed by notificationService
                                    // The service handles the notification manager and overlay connections
                                    window.notificationModuleItem = item;
                                }
                                if (modelData === "wallpaper" && item) {
                                    // wallpaper widget now managed by wallpaperService
                                    // The service handles the wallpaper carousel connection
                                    if (wallpaperServiceLoader.item && wallpaperServiceLoader.item.carousel) {
                                        item.wallpaperCarousel = wallpaperServiceLoader.item.carousel;
                                    }
                                }
                            }
                        }
                    }
                }

                Text {
                    text: "】"
                    color: theme ? theme.accent : "#3ddbd9"
                    font.pixelSize: 14
                    font.family: "JetBrains Mono, monospace"
                    anchors.verticalCenter: parent.verticalCenter
                }
            }
        }
    }

    MouseArea {
        anchors.fill: parent
        z: -1
        onClicked: {
            if (ipcPanelManager)
                ipcPanelManager.hideAllPanels();
        }
    }

    GlobalShortcut {
        name: "wallpaper-carousel"
        description: "Toggle wallpaper carousel"

        onPressed: {
            if (debuggerInstance)
                debuggerInstance.logNotification("GlobalShortcut: Wallpaper carousel shortcut pressed");
            var process = Qt.createQmlObject('import Quickshell.Io; Process {}', bar);
            process.exec(["qs", "ipc", "call", "wallpaper", "toggle"]);
        }
    }

    function getScreenConfig(screenIndex) {
        if (screenIndex < screenConfigs.length) {
            return screenConfigs[screenIndex];
        }
        return {
            left: ["workspaces"],
            center: ["time", "mpris"],
            right: ["system"]
        };
    }

    function getWorkspaceRange(screenIndex) {
        if (screenIndex < workspaceRanges.length) {
            return workspaceRanges[screenIndex];
        }
        return [1, 10];
    }

    function getModuleSource(moduleName) {
        var moduleFileMap = {
            "workspaces": "hyprlandWorkspaces",
            "windowTitle": "windowTitleWidget",
            "time": "calendarModules/timeWidget",
            "system": "systemIndicators",
            "mpris": "mprisModules/mprisWidget",
            "volume": "volumeWidget",
            "network": "networkWidget",
            "notification": "notificationModules/notificationWidget",
            "wallpaper": "wallpaperModules/wallpaperWidget"
        };
        var fileName = moduleFileMap[moduleName];
        if (fileName) {
            return "./modules/" + fileName + ".qml";
        }
        return "";
    }
}
