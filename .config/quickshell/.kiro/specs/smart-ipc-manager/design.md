# Design Document

## Overview

The Smart IPC Manager enhances the existing IPC system to provide intelligent state tracking and JSON-based IPC endpoints for all panel overlays. The system extends the current panel management functionality to include standardized IPC calls that return JSON objects containing panel states for calendar, notification, wallpaper, and mpris panels.

The design follows the functional programming paradigm established in the codebase and integrates seamlessly with the existing panel management system without requiring new files.

## Architecture

### Current System Analysis

The existing system consists of:
- `ipcPanelManager.qml`: Singleton managing panel registration, visibility, and state
- `calendarOverlay.qml`: Calendar panel with show/hide functionality
- `notificationOverlay.qml`: Notification panel with show/hide functionality
- `wallpaperCarousel.qml`: Wallpaper panel with overlay functionality
- `mprisOverlay.qml`: MPRIS media player panel with overlay functionality
- IPC handlers in `bar.qml` for wallpaper and mpris targets accessible via `qs ipc show`

### Enhanced Architecture

The enhanced system will:
1. Extend `ipcPanelManager.qml` with IPC endpoint functions
2. Add JSON state serialization capabilities
3. Integrate with existing panel registration system
4. Provide consistent API for all panel types

```mermaid
graph TB
    A[qs ipc call] --> B[IPC Manager]
    B --> C[Panel State Tracker]
    C --> D[JSON Serializer]
    D --> E[Response]
    
    F[Calendar Overlay] --> C
    G[Notification Overlay] --> C
    H[Other Panels] --> C
    
    C --> I[Panel Registry]
    I --> J[Visibility States]
    I --> K[Configuration Data]
```

## Components and Interfaces

### 1. Enhanced IPC Panel Manager

**Location**: `bar/modules/ipcPanelManager.qml`

**New Functions**:
- `getCalendarState()`: Returns calendar overlay state as JSON
- `getNotificationState()`: Returns notification overlay state as JSON
- `getWallpaperState()`: Returns wallpaper overlay state as JSON
- `getMprisState()`: Returns mpris overlay state as JSON
- `getAllPanelStates()`: Returns comprehensive panel state JSON
- `serializePanel(panelId)`: Converts panel data to JSON format

**JSON Response Schema**:
```json
{
  "panelId": "string",
  "visible": "boolean",
  "lastShown": "number",
  "active": "boolean",
  "configuration": {
    "width": "number",
    "height": "number",
    "position": "object"
  },
  "metadata": {
    "type": "string",
    "capabilities": "array"
  }
}
```

### 2. Panel State Integration

**Calendar Overlay Integration**:
- Register calendar overlay with IPC manager on component completion
- Update state when `overlayVisible` changes
- Provide configuration data (dimensions, position)

**Notification Overlay Integration**:
- Register notification overlay with IPC manager on component completion
- Update state when `overlayVisible` changes
- Include notification count in state data

### 3. IPC Endpoint Functions

**Calendar IPC Function**:
```javascript
function getCalendarState() {
    const calendarPanel = registeredPanels["calendar"];
    if (!calendarPanel) {
        return JSON.stringify({
            error: "Calendar panel not registered",
            available: false
        });
    }
    
    return JSON.stringify({
        panelId: "calendar",
        visible: calendarPanel.visible,
        lastShown: calendarPanel.lastShown,
        active: currentActivePanel === "calendar",
        configuration: {
            width: 450,
            height: 280,
            position: "top-left"
        },
        metadata: {
            type: "calendar",
            capabilities: ["date-selection", "timer"]
        }
    });
}
```

**Notification IPC Function**:
```javascript
function getNotificationState() {
    const notificationPanel = registeredPanels["notification"];
    if (!notificationPanel) {
        return JSON.stringify({
            error: "Notification panel not registered",
            available: false
        });
    }
    
    // Access notification count from notification manager
    const notificationCount = getNotificationCount();
    
    return JSON.stringify({
        panelId: "notification",
        visible: notificationPanel.visible,
        lastShown: notificationPanel.lastShown,
        active: currentActivePanel === "notification",
        configuration: {
            width: 380,
            height: "auto",
            position: "top-right"
        },
        metadata: {
            type: "notification",
            capabilities: ["history", "clear-all"],
            notificationCount: notificationCount
        }
    });
}
```

## Data Models

### Panel State Model
```javascript
{
    panelId: String,           // Unique identifier
    visible: Boolean,          // Current visibility state
    lastShown: Number,         // Timestamp of last show
    active: Boolean,           // Whether this is the active panel
    configuration: {
        width: Number,
        height: Number|String,
        position: String
    },
    metadata: {
        type: String,          // Panel type identifier
        capabilities: Array,   // Available features
        [customFields]: Any    // Panel-specific data
    }
}
```

### Error Response Model
```javascript
{
    error: String,             // Error description
    available: Boolean,        // Whether panel is available
    timestamp: Number          // Error timestamp
}
```

## Error Handling

### Panel Not Registered
- Return standardized error JSON with `available: false`
- Log error message for debugging
- Maintain consistent response structure

### Invalid Panel State
- Validate panel object before serialization
- Use `validatePanels()` function to clean invalid entries
- Return error response for corrupted panel data

### JSON Serialization Errors
- Wrap JSON.stringify in try-catch blocks
- Return fallback error response on serialization failure
- Log detailed error information

### IPC Call Failures
- Provide meaningful error messages through command-line interface
- Ensure error responses follow JSON schema
- Include troubleshooting information in error messages

## Testing Strategy

### Unit Testing Approach
1. **Panel Registration Testing**
   - Verify calendar and notification panels register correctly
   - Test registration with invalid parameters
   - Validate panel object structure

2. **State Serialization Testing**
   - Test JSON output format consistency
   - Verify all required fields are present
   - Test error response formats

3. **IPC Function Testing**
   - Test `getCalendarState()` with various panel states
   - Test `getNotificationState()` with different notification counts
   - Test error conditions and edge cases

### Integration Testing
1. **Command Line Testing**
   - Use `qs ipc show` to verify new targets appear
   - Use `qs ipc call` with calendar and notification targets
   - Verify JSON responses are valid and parseable

2. **Panel Interaction Testing**
   - Test state updates when panels are shown/hidden
   - Verify only one panel visible at a time
   - Test state consistency during panel transitions

### Functional Testing
1. **Panel Visibility Management**
   - Test single panel visibility rule
   - Verify state tracking accuracy
   - Test panel switching behavior

2. **JSON Response Validation**
   - Validate response schema compliance
   - Test camelCase naming consistency
   - Verify data type correctness

## Implementation Considerations

### Functional Programming Compliance
- Use pure functions where possible for state serialization
- Minimize side effects in IPC functions
- Follow existing patterns from wallpaper and mpris implementations

### Performance Optimization
- Cache panel state data to avoid repeated calculations
- Use efficient JSON serialization
- Minimize object creation in frequently called functions

### Backward Compatibility
- Maintain existing `ipcPanelManager.qml` API
- Ensure existing panel functionality remains unchanged
- Add new features without breaking current integrations

### Extensibility
- Design JSON schema to accommodate future panel types
- Use consistent naming conventions for easy parsing
- Provide hooks for custom panel metadata