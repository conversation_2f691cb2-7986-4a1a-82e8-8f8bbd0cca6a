# Implementation Plan

- [x] 1. Create service file structure and interfaces
  - Create `bar/modules/services/mprisService.qml` with basic QtObject structure and interface properties
  - Create `bar/modules/services/notificationService.qml` with basic QtObject structure and interface properties
  - Create `bar/modules/services/calendarService.qml` with basic QtObject structure and interface properties
  - Create `bar/modules/services/wallpaperService.qml` with basic QtObject structure and interface properties
  - Create `bar/modules/services/ipcManagerService.qml` with basic QtObject structure and interface properties
  - Test each service file loads correctly from bar.qml using `qs` command with timeout
  - _Requirements: 1.1, 6.1, 8.1_

- [x] 2. Implement mprisService with loader management
  - Move mprisWidget and mprisOverlay loaders from bar.qml to mprisService.qml
  - Implement showOverlay(), hideOverlay(), and toggleOverlay() functions in mprisService
  - Add widget and overlay properties that expose loaded items to bar.qml
  - Update bar.qml to reference mprisService.widget instead of direct loader
  - Test MPRIS functionality works identically using `qs ipc call mpris toggle` with timeout
  - _Requirements: 1.1, 1.2, 5.1, 7.1_

- [x] 3. Implement notificationService with loader management
  - Move notificationWidget, notificationOverlay, and notificationManager loaders from bar.qml to notificationService.qml
  - Implement showOverlay(), hideOverlay(), and toggleOverlay() functions in notificationService
  - Add widget, overlay, and manager properties that expose loaded items to bar.qml
  - Update bar.qml to reference notificationService.widget instead of direct loader
  - Test notification functionality works identically using `qs ipc call notification toggle` with timeout
  - _Requirements: 1.1, 1.2, 5.1, 7.1_

- [x] 4. Implement calendarService with loader management
  - Move timeWidget and calendarOverlay loaders from bar.qml to calendarService.qml
  - Implement showOverlay(), hideOverlay(), and toggleOverlay() functions in calendarService
  - Add widget and overlay properties that expose loaded items to bar.qml
  - Update bar.qml to reference calendarService.widget instead of direct loader
  - Test calendar functionality works identically using `qs ipc call calendar toggle` with timeout
  - _Requirements: 1.1, 1.2, 5.1, 7.1_

- [x] 5. Implement wallpaperService with loader management
  - Move wallpaperWidget and wallpaperCarousel loaders from bar.qml to wallpaperService.qml
  - Implement showOverlay(), hideOverlay(), and toggleOverlay() functions in wallpaperService
  - Add widget and carousel properties that expose loaded items to bar.qml
  - Update bar.qml to reference wallpaperService.widget instead of direct loader
  - Test wallpaper functionality works identically using `qs ipc call wallpaper toggle` with timeout
  - _Requirements: 1.1, 1.2, 5.1, 7.1_

- [x] 6. Create centralized ipcManagerService
  - Move ipcPanelManager loader from bar.qml to ipcManagerService.qml
  - Move all IPC handlers (wallpaper, mpris, calendar, notification) from bar.qml to ipcManagerService.qml
  - Implement centralized panel registration function that services can call
  - Update all services to register their panels through ipcManagerService
  - Test all IPC functionality using `qs ipc show` and `qs ipc call` commands with timeout
  - _Requirements: 2.1, 2.2, 2.3, 6.1_

- [x] 7. Update bar.qml to use service architecture
  - Replace all direct loaders in bar.qml with service loaders
  - Update all component references to use service properties (e.g., mprisService.widget)
  - Remove all IPC handlers from bar.qml since they're now in ipcManagerService
  - Update Component.onCompleted to initialize services instead of direct components
  - Test complete bar functionality using `qs` commands with timeout
  - _Requirements: 1.1, 1.3, 2.2, 6.1_

- [x] 8. Convert all naming to camelCase convention
  - Review all variable names in bar.qml and convert to camelCase
  - Review all function names in bar.qml and convert to camelCase
  - Review all property names in service files and convert to camelCase
  - Update any references to renamed variables/functions throughout the codebase
  - Test functionality after naming changes using `qs` commands with timeout
  - _Requirements: 3.1, 3.2, 3.3_

- [x] 9. Implement service overlay management
  - Update each service to handle its own overlay visibility state
  - Implement overlay registration with ipcManagerService in each service's Component.onCompleted
  - Remove overlay management code from bar.qml since services now handle their own overlays
  - Ensure only one overlay visible at a time through ipcManagerService coordination
  - Test overlay show/hide functionality for each service using `qs ipc call` with timeout
  - _Requirements: 5.1, 5.2, 5.3, 5.4_

- [ ] 10. Clean up bar.qml and remove unused code
  - Remove all moved loaders from bar.qml
  - Remove all moved IPC handlers from bar.qml
  - Remove unused properties and functions from bar.qml
  - Simplify Component.onCompleted to only handle bar-specific initialization
  - Test complete functionality to ensure nothing was broken during cleanup using `qs` commands with timeout
  - _Requirements: 1.1, 2.2, 7.1_

- [ ] 11. Implement theme and context propagation
  - Update all services to accept parentTheme, targetScreen, and debuggerInstance properties
  - Ensure theme propagation from bar.qml to all services and their loaded components
  - Update all service loaders to pass theme context to loaded items
  - Test theme consistency across all components using visual inspection and `qs` commands
  - _Requirements: 6.2, 7.1_

- [ ] 12. Final testing and validation
  - Test all existing functionality works identically to before refactoring
  - Verify all IPC commands work using `qs ipc show` and `qs ipc call` for each target
  - Test overlay management ensures only one overlay visible at a time
  - Verify no qmldir files were created during refactoring
  - Validate camelCase naming convention is used consistently throughout
  - Test complete user workflow to ensure no behavioral changes
  - _Requirements: 4.1, 4.2, 4.3, 7.1, 7.2, 7.3, 8.1_