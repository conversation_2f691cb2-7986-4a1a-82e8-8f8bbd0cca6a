# Design Document

## Overview

The Bar Refactoring design transforms the current monolithic bar.qml structure into a clean, service-oriented architecture. The main bar.qml will focus solely on layout and coordination, while individual service files will manage their own loaders, overlays, and IPC handlers. This creates a more maintainable codebase where each service is self-contained and the main bar file remains simple and readable.

The refactoring follows the existing functional programming patterns and maintains all current functionality while dramatically improving code organization and maintainability.

## Architecture

### Current System Analysis

The current bar.qml contains:
- Multiple Loader components for various modules (theme, debugger, IPC manager, overlays)
- IPC handlers for wallpaper, mpris, calendar, and notification
- Complex overlay management and registration logic
- Mixed concerns between layout, loading, and service management
- Over 600 lines of mixed responsibilities

### Target Architecture

The refactored system will have:
- Clean bar.qml focused on layout and coordination (under 200 lines)
- Service files that manage their own loaders and overlays
- Centralized IPC management through the IPC manager service
- Clear separation of concerns between layout and service logic
- Consistent camelCase naming throughout

```mermaid
graph TB
    A[bar.qml - Layout Only] --> B[mprisService.qml]
    A --> C[notificationService.qml]
    A --> D[calendarService.qml]
    A --> E[wallpaperService.qml]
    A --> F[ipcManagerService.qml]
    
    B --> G[mprisWidget.qml]
    B --> H[mprisOverlay.qml]
    
    C --> I[notificationWidget.qml]
    C --> J[notificationOverlay.qml]
    C --> K[notificationManager.qml]
    
    D --> L[timeWidget.qml]
    D --> M[calendarOverlay.qml]
    
    E --> N[wallpaperWidget.qml]
    E --> O[wallpaperCarousel.qml]
    
    F --> P[All IPC Handlers]
    F --> Q[Panel Registration]
```

## Components and Interfaces

### 1. Refactored bar.qml

**Purpose**: Layout and coordination only
**Size**: Under 200 lines
**Responsibilities**:
- Define screen configuration and layout
- Load service files
- Provide theme and screen context to services
- Handle global mouse events

**Interface**:
```qml
PanelWindow {
    id: window
    
    property var targetScreen: Quickshell.screens[0]
    property var theme: themeLoader.item
    
    // Service loaders
    property var mprisService: mprisServiceLoader.item
    property var notificationService: notificationServiceLoader.item
    property var calendarService: calendarServiceLoader.item
    property var wallpaperService: wallpaperServiceLoader.item
    property var ipcManagerService: ipcManagerServiceLoader.item
    
    // Layout only - no business logic
    RowLayout {
        // Module references through services
    }
}
```

### 2. Service Files Architecture

#### mprisService.qml
**Purpose**: Manage all MPRIS-related components and functionality
**Responsibilities**:
- Load mprisWidget and mprisOverlay
- Handle MPRIS IPC registration
- Manage overlay visibility and state
- Provide clean interface to bar.qml

**Interface**:
```qml
QtObject {
    id: mprisService
    
    property var widget: mprisWidgetLoader.item
    property var overlay: mprisOverlayLoader.item
    property bool overlayVisible: false
    
    signal overlayToggleRequested()
    
    function showOverlay() { /* implementation */ }
    function hideOverlay() { /* implementation */ }
}
```

#### notificationService.qml
**Purpose**: Manage all notification-related components and functionality
**Responsibilities**:
- Load notificationWidget, notificationOverlay, and notificationManager
- Handle notification IPC registration
- Manage notification state and count
- Coordinate between widget and overlay

**Interface**:
```qml
QtObject {
    id: notificationService
    
    property var widget: notificationWidgetLoader.item
    property var overlay: notificationOverlayLoader.item
    property var manager: notificationManagerLoader.item
    property bool overlayVisible: false
    
    signal overlayToggleRequested()
    
    function showOverlay() { /* implementation */ }
    function hideOverlay() { /* implementation */ }
}
```

#### calendarService.qml
**Purpose**: Manage all calendar-related components and functionality
**Responsibilities**:
- Load timeWidget and calendarOverlay
- Handle calendar IPC registration
- Manage calendar overlay state
- Coordinate time widget and overlay interactions

#### wallpaperService.qml
**Purpose**: Manage all wallpaper-related components and functionality
**Responsibilities**:
- Load wallpaperWidget and wallpaperCarousel
- Handle wallpaper IPC registration
- Manage wallpaper selection and application
- Coordinate widget and carousel interactions

#### ipcManagerService.qml
**Purpose**: Centralize all IPC functionality
**Responsibilities**:
- Load and configure ipcPanelManager
- Register all IPC handlers (wallpaper, mpris, calendar, notification)
- Provide centralized IPC interface
- Handle panel registration and management

### 3. Service Interface Pattern

All services will follow a consistent interface pattern:

```qml
QtObject {
    id: serviceRoot
    
    // Required properties from bar.qml
    property var parentTheme: null
    property var targetScreen: null
    property var debuggerInstance: null
    
    // Service-specific widget/overlay properties
    property var widget: widgetLoader.item
    property var overlay: overlayLoader.item
    
    // State properties
    property bool overlayVisible: false
    
    // Loaders for components
    Loader {
        id: widgetLoader
        source: "./widgetFile.qml"
        onLoaded: {
            if (item && parentTheme) {
                item.parentTheme = parentTheme
            }
        }
    }
    
    // Service interface functions
    function showOverlay() { /* implementation */ }
    function hideOverlay() { /* implementation */ }
    function toggleOverlay() { /* implementation */ }
    
    // Internal initialization
    Component.onCompleted: {
        // Register with IPC manager if needed
        // Set up internal connections
    }
}
```

## Data Models

### Service Configuration Model
```javascript
{
    serviceName: String,        // Service identifier
    widgetPath: String,         // Path to widget QML file
    overlayPath: String,        // Path to overlay QML file (optional)
    hasIpc: Boolean,           // Whether service needs IPC registration
    ipcTarget: String,         // IPC target name
    dependencies: Array        // Other services this depends on
}
```

### Bar Layout Model
```javascript
{
    screenConfigs: [
        {
            left: Array,       // Module names for left section
            center: Array,     // Module names for center section
            right: Array       // Module names for right section
        }
    ],
    availableModules: {
        moduleName: serviceName  // Mapping of module names to services
    }
}
```

## Error Handling

### Service Loading Errors
- Each service loader will have error handling for failed component loading
- Fallback behavior when services fail to initialize
- Logging of service loading issues through debugger

### IPC Registration Errors
- Centralized error handling in ipcManagerService
- Graceful degradation when IPC registration fails
- Clear error messages for debugging IPC issues

### Overlay Management Errors
- Service-level error handling for overlay show/hide failures
- State consistency checks to prevent overlay conflicts
- Recovery mechanisms for corrupted overlay states

## Testing Strategy

### Component Testing
1. **Service Loading Tests**
   - Test each service loads its components correctly
   - Verify service interfaces work as expected
   - Test error handling for failed component loads
   - **CRITICAL**: Always restart qs after changes and verify widgets appear in the bar

2. **IPC Functionality Tests**
   - Use `qs ipc show` to verify all targets are available
   - Use `qs ipc call` to test each service's IPC functionality
   - Test with timeout mechanisms for reliability
   - **CRITICAL**: Test IPC functionality after restarting qs, not just during development

3. **Overlay Management Tests**
   - Test overlay show/hide functionality for each service
   - Verify only one overlay visible at a time
   - Test overlay state consistency
   - **CRITICAL**: Test overlays after full qs restart to ensure proper loading

4. **Widget Visibility Tests**
   - **CRITICAL**: After every service implementation, restart qs completely
   - Verify all widgets appear correctly in the bar layout
   - Check that service-managed widgets are properly reparented and visible
   - Test that widget dimensions and positioning work correctly

### Integration Testing
1. **Bar Layout Tests**
   - Verify all modules appear in correct layout positions
   - Test theme propagation to all services
   - Test screen configuration handling

2. **Service Communication Tests**
   - Test service-to-service communication
   - Verify IPC manager coordinates all services correctly
   - Test global event handling (mouse clicks, shortcuts)

### Functional Testing
1. **Feature Parity Tests**
   - Verify all existing functionality works identically
   - Test all user interactions work as before
   - Validate no behavioral changes for end users

2. **Performance Tests**
   - Measure startup time before and after refactoring
   - Test memory usage with new service architecture
   - Verify no performance degradation

## Implementation Considerations

### camelCase Naming Convention
- All variables, functions, and properties will use camelCase
- Existing snake_case or other naming will be converted
- Consistent naming across all service files

### No qmldir Files
- All imports will use direct file paths (e.g., "./modules/service.qml")
- No module definition files will be created
- Simple, direct import structure maintained

### Functional Programming Compliance
- Services will use pure functions where possible
- Minimize side effects in service interfaces
- Follow existing patterns from current codebase

### Backward Compatibility
- All existing functionality must work identically
- No breaking changes to user experience
- Maintain all current IPC endpoints and behavior

### Testing Requirements
- Every refactored component must be tested after completion
- Use qs commands for IPC testing
- Implement timeout mechanisms for reliable testing
- Test configuration after each task completion

## Migration Strategy

### Phase 1: Create Service Files
- Create empty service files with basic structure
- Implement service interfaces without functionality
- Test service loading from bar.qml

### Phase 2: Move Loaders
- Move component loaders from bar.qml to respective services
- Update bar.qml to reference services instead of direct loaders
- Test each service as loaders are moved

### Phase 3: Move IPC Handlers
- Move all IPC handlers from bar.qml to ipcManagerService
- Update IPC registration to use centralized service
- Test all IPC functionality after migration

### Phase 4: Clean Up and Optimize
- Remove unused code from bar.qml
- Optimize service interfaces
- Final testing and validation

This phased approach ensures functionality is maintained throughout the refactoring process and allows for testing at each step.