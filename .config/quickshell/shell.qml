import QtQuick
import Quickshell

ShellRoot {
    Variants {
        model: Quickshell.screens

        delegate: Component {
            Loader {
                required property var modelData
                source: "./bar/bar.qml"
                onLoaded: {
                    item.targetScreen = modelData;
                    item.screenIndex = Quickshell.screens.indexOf(modelData);
                }
            }
        }
    }
}
